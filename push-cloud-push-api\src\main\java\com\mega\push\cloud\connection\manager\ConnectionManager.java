package com.mega.push.cloud.connection.manager;

import com.eatthepath.pushy.apns.ApnsClient;
import com.eatthepath.pushy.apns.ApnsClientBuilder;
import com.eatthepath.pushy.apns.util.SimpleApnsPushNotification;
//import com.huawei.push.messaging.HuaweiApp;
//import com.huawei.push.messaging.HuaweiCredential;
//import com.huawei.push.messaging.HuaweiMessaging;
//import com.huawei.push.messaging.HuaweiOption;
import com.mega.push.cloud.common.entity.ConfigAndroid;
import com.mega.push.cloud.common.entity.ConfigIos;
import com.mega.push.cloud.config.Config;
import com.mega.push.cloud.core.utils.JsonUtils;
import com.mega.push.cloud.po.PushNotificationPO;
import com.xiaomi.xmpush.server.Sender;
import io.netty.channel.nio.NioEventLoopGroup;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;

import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.*;
@Slf4j
public class ConnectionManager {
    //<appid,Config>
    public static final Map<String, Config> configMap = new ConcurrentHashMap<>();

    //<appIdBusiId,推送队列>
    public static final Map<String, ConcurrentLinkedDeque<SimpleApnsPushNotification>> pushNotificationMap = new ConcurrentHashMap<>();

    //<config_android_id,推送队列>
    public static final Map<String, ConcurrentLinkedDeque<PushNotificationPO>> androidPushNotificationMap = new ConcurrentHashMap<>();

    //<appIdAuthId,推送客户端>
    public static final Map<String, ApnsClient> apnsClientMap = new ConcurrentHashMap<>();

    //<config_android_id,华为推送客户端>
//    public static final Map<String, HuaweiMessaging> huaWeiClientMap = new ConcurrentHashMap<>();
    
 //<PushLoggerId,PushNum>
    public static final Map<Long, Integer> pushCountMap = new ConcurrentHashMap<>();

    public static final ArrayBlockingQueue<ConfigIos> apnsAppMap = new ArrayBlockingQueue<>(16);
    public static final File directory = new File("");

    /***
     * 创建APNS推送客户端
     * @param configIos
     * @return
     */
    public static ApnsClient createApnsClient(ConfigIos configIos){
        log.info("\n");
        log.info("--- 创建ios推送client start ---");
        ApnsClient apnsClient = null;
        try {
            String url = directory.getCanonicalPath()+File.separator+configIos.getCerPath();
//            System.out.println(url);
            log.info("config IOS :" + JsonUtils.toJson(configIos));
            log.info("CerPath : "+ url);
            int num = Runtime.getRuntime().availableProcessors();
//            log.info("cpu thread num : " + num);
            log.info("busiId: {}", configIos.getBusiId());
            String apnsServer = configIos.getBusiId().equals("dev")?ApnsClientBuilder.DEVELOPMENT_APNS_HOST:ApnsClientBuilder.PRODUCTION_APNS_HOST;
            log.info("apnsServer: {}", apnsServer);
            apnsClient = new ApnsClientBuilder()
                    .setApnsServer(apnsServer)
                    .setConcurrentConnections(num)
                    .setEventLoopGroup(new NioEventLoopGroup(num))
                    .setClientCredentials(new File(url), configIos.getCerPwd())
                    .build();
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (apnsClient != null){
            apnsClientMap.put(configIos.getAppId()+"."+configIos.getApplicationAuthId(),apnsClient);
        }
        log.info(" --- 创建ios推送client end!---\n");
        return apnsClient;
    }


//    public static HuaweiMessaging createHWClient(ConfigAndroid configAndroid){
//
//        HuaweiCredential credential = HuaweiCredential.builder()
//                .setAppId(configAndroid.getClientKey())
//                .setAppSecret(configAndroid.getClientSecret())
//                .build();
//        HuaweiOption option = HuaweiOption.builder()
//                .setCredential(credential)
//                .build();
//        HuaweiApp app = HuaweiApp.getInstance(option);
//        return  HuaweiMessaging.getInstance(app);
//    }

    private static final OkHttpClient connectionManager = new OkHttpClient
            .Builder()
            .readTimeout(3, TimeUnit.SECONDS)
            .writeTimeout(3,TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(128,1,TimeUnit.SECONDS))
            .build();

    public static OkHttpClient getConnectionManager() {
        return connectionManager;
    }

}
