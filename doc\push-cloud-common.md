# push-cloud-common 模块文档

## 概述
push-cloud-common 模块包含项目中通用的配置、实体类和映射器。

## 主要功能
- **CommonConfig.java**: 通用配置类。
- **ApplicationAuth.java**: 应用授权实体类。
- **ApplicationInfo.java**: 应用信息实体类。
- **BadToken.java**: 无效令牌实体类。
- **ConfigAndroid.java**: Android配置实体类。
- **ConfigIos.java**: iOS配置实体类。
- **PushLogger.java**: 推送日志实体类。
- **ApplicationAuthMapper.java**: 应用授权映射器。
- **ApplicationInfoMapper.java**: 应用信息映射器。
- **BadTokenMapper.java**: 无效令牌映射器。
- **ConfigAndroidMapper.java**: Android配置映射器。
- **ConfigIosMapper.java**: iOS配置映射器。
- **PushLoggerMapper.java**: 推送日志映射器。

## 使用方法
请参考源代码中的注释和文档。