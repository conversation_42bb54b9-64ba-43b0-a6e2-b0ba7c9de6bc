logging:
  file:
    path: /opt/log/${spring.application.name}-${server.port}/
    max-size: 128MB
spring:
  cloud:
    consul:
      enabled: false
      host: **************
      port: 8500
      discovery:
        prefer-ip-address: true
        ip-address: **************
        instance-id: ${spring.application.name}-${server.port}
        service-name: ${spring.application.name}
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    username: push_cloud
    password: Mangosteen0!
    maximum-pool-size: 10
  redis:
    host: 127.0.0.1
    port: 6379