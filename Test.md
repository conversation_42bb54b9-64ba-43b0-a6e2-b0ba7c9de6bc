# 1 小米推送测试

### token申请
入参json

```json
{
  "authKey": "weoqwelm",
  "authSecret": "wre3c1wb"
}
```

### 发送推送
token = eyJub3ciOjE3NDc2MzY2ODksImV4cGlyZSI6MTc0NzYzNzU4OSwicmFuZF9udW0iOiJaRHEifQ==.vo+cxGiDnISRqhftdr5EPjwvI0uCOAVzqN9FXkvjIVU0Vp3j5aO61QguEWVP8/6fHvGfBVWZrs/Q2z/b5xxpU9oKDAdwmtbjysXctOMFqCsJBEilBo1Wic71Y7xWllA9VDyuyQVoUGT6FNfygnppqQ==.PhmHQBxTVRoqmmPNPScJQw==
```json
{
  "appId": 1000004,
  "authSecret": "wre3c1wb",
  "authToken": "eyJub3ciOjE3NDc2MzY2ODksImV4cGlyZSI6MTc0NzYzNzU4OSwicmFuZF9udW0iOiJaRHEifQ==.vo+cxGiDnISRqhftdr5EPjwvI0uCOAVzqN9FXkvjIVU0Vp3j5aO61QguEWVP8/6fHvGfBVWZrs/Q2z/b5xxpU9oKDAdwmtbjysXctOMFqCsJBEilBo1Wic71Y7xWllA9VDyuyQVoUGT6FNfygnppqQ==.PhmHQBxTVRoqmmPNPScJQw==",
  "deviceList": [
    "AmLqWIIij0bZVPNXhVH+yHHqHM105+M/u/m1+A6BW5HOWnecq+4RrDqYPsi3xQhw"
  ],
  "messagePO": {
    "androidActivityType": 1,
    "channelId": 136201,
    "description": "快鸟加速器通知描述",
    "payload": "这是一个测试消息的payload",
    "title": "快鸟加速器通知"
  }
}
```
# 2 ios推送测试

### token神奇
```json
{
  "authKey": "ashdasdkl",
  "authSecret": "isdn2n1isa"
}
```
token = eyJub3ciOjE3NDc2NDM4ODQsImV4cGlyZSI6MTc0NzY0NDc4NCwicmFuZF9udW0iOiJLR1oifQ==.Yqk4GBdrdysYmI2Z2ffj7LgHGq3iOREJ0a8kj2W5QdY980h9qut6ONLwhTVcksBlCKhkkLsTbRR0+QSZSo6hRIbdTDqCYXEfUx9cWfIUClroI5Oxa/Is9f24LMsZp4N0E6PTTHS76+e55H4A/CDBUQ==.y2UrS0PeoiSrU6DdZdXEXw==

### 发送
```json
{
  "appId": 1000001,
  "authSecret": "isdn2n1isa",
  "authToken": "eyJub3ciOjE3NDc2NDc0NDQsImV4cGlyZSI6MTc0NzY0ODM0NCwicmFuZF9udW0iOiJUWncifQ==.OE22BioJ+PtcpmmxNqjBt7gHGq3iOREJ0a8kj2W5QdalG2wXj7xS67nPF8RM2SRrx04FP9YvTeeYqY0ouXTLoEZhMIn0cT0dG7efwdIElJ/SDBOJ06CNkrVAaJ71gp4lE6PTTHS76+e55H4A/CDBUQ==.PH0tjcXpdVpMbczVu7RvnA==",
  "deviceList": [
    "61116e2c119eb206bf15f4dc8e85e16050c3f85e2e5ade48716be3a6137dfff5"
  ],
  "messagePO": {
    "description": "ios通知内容",
    "iosBusiId": "dev",
    "payload": "ios通知payload",
    "title": "ios通知标题"
  }
}
```

## 错误

### 100004  token错误
{
  "code": 10004,
  "message": "token length is not valid"
}