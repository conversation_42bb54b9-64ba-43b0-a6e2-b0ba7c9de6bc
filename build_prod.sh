#!/bin/bash
if [ "$4" == "stop" -o "$4" == "restart" ]; then
  pid=$(ps -ef | grep 'java' | grep $3 | grep $2 | grep $1 | grep -v grep | awk '{print $2'})
  if [ -z "$pid" ]; then
   echo 'there are not push-cloud process. starting will be continue.'
  fi
  if [ -n "$pid" ]; then
   echo 'java process id is '$pid
   if ps -p $pid > /dev/null
   then
    echo $pid' will be kill'
    kill -9 $pid
   fi
  fi
fi

if [ "$4" == "start" -o "$4" == "restart" ]; then
  echo 'start push-cloud wait.'
  nohup java -Xms1G -Xmx1G -jar /opt/push-cloud-$1/$3/$3.jar --spring.profiles.active=$1 --server.port=$2 > /dev/null 2>log &
  sleep 10s
  echo 'finish starting push-cloud'
fi
