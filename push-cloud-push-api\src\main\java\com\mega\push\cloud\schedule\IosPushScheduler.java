package com.mega.push.cloud.schedule;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.mega.push.cloud.service.ApnsService;

/***
 * ios推送调度器
 */
@Service
@Slf4j
public class IosPushScheduler {

    private final ApnsService apnsService;

    @Autowired
    public IosPushScheduler(ApnsService apnsService) {
        this.apnsService = apnsService;
    }

    /***
     * ios推送调度器
     */
    @Scheduled(fixedDelay = 1000) // 1s
    public void scheduleapnsAppMap() {
        // 获取配置
        this.apnsService.scheduleApnsAppMap();
    }

}
