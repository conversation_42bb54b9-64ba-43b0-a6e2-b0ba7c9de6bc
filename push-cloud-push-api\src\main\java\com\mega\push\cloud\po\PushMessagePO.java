package com.mega.push.cloud.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel("推送额外参数")
@Data
public class PushMessagePO {

    @ApiModelProperty("推送标题")
    @NotNull
    private String title;

    @ApiModelProperty("推送内容")
    private String description;

    @ApiModelProperty("推送额外信息")
    private String payload;

    @ApiModelProperty(value="渠道id, 小米系统channel_id:136201",example = "136201")
    private Integer channelId;

    @ApiModelProperty(value="android 点击通知后打开的Activity, 0:不打开 1:打开应用launcher 2:打开任意Activity 3:打开网页", example = "1")
    private Integer androidActivityType = 0; // 1: 打开应用 2: 打开应用Activity 3: 打开url 

    @ApiModelProperty(value="android 点击通知后打开的Activity uri, androidActivityType为2时必传", example = "intent:#Intent;component=com.xiaomi.mipushdemo/.NewsActivity;end")
    private String androidActivityUri;

    @ApiModelProperty(value="android 点击通知后打开的网页, androidActivityType为3时必传", example = "https://www.baidu.com")
    private String androidWebUri;


    @ApiModelProperty(value="ios 业务id", example = "c")
    private String iosBusiId;

    @ApiModelProperty(value="ios 声音", example = "coins.mp3")
    private String iosSound;

    @ApiModelProperty(value="ios 角标", example = "1")
    private Integer badgeNumber;


}
