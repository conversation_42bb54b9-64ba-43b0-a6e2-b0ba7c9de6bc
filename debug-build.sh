#!/bin/bash

# 调试构建过程的脚本
echo "=== 调试Maven构建过程 ==="

echo "1. 检查项目根目录结构："
ls -la

echo ""
echo "2. 检查push-cloud-push-api目录："
ls -la push-cloud-push-api/

echo ""
echo "3. 检查push-cloud-push-api/target目录（如果存在）："
if [ -d "push-cloud-push-api/target" ]; then
    ls -la push-cloud-push-api/target/
    echo ""
    echo "target目录中的JAR文件："
    find push-cloud-push-api/target/ -name "*.jar" -type f
else
    echo "target目录不存在"
fi

echo ""
echo "4. 检查cerpath目录："
if [ -d "cerpath" ]; then
    ls -la cerpath/
else
    echo "cerpath目录不存在"
fi

echo ""
echo "5. 检查lib目录："
if [ -d "push-cloud-push-api/src/main/java/lib" ]; then
    ls -la push-cloud-push-api/src/main/java/lib/
else
    echo "lib目录不存在"
fi

echo ""
echo "6. 检查Dockerfile："
if [ -f "push-cloud-push-api/Dockerfile" ]; then
    echo "Dockerfile存在"
    cat push-cloud-push-api/Dockerfile
else
    echo "Dockerfile不存在"
fi

echo ""
echo "=== 调试完成 ==="
