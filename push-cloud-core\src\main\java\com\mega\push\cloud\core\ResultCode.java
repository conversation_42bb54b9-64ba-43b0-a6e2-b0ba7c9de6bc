package com.mega.push.cloud.core;

import com.mega.push.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum ResultCode {

    SUCCESS(1),
    ERROR(0),
    NOT_AUTHENTICATE_ERROR(2),
    NOT_AUTHORIZE_ERROR(3),
    PARAM_ERROR(4);

    private final Integer code;

    ResultCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
