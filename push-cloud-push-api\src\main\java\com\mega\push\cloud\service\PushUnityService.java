package com.mega.push.cloud.service;

import com.mega.push.cloud.common.entity.ApplicationAuth;
import com.mega.push.cloud.common.entity.ConfigAndroid;
import com.mega.push.cloud.common.entity.ConfigIos;
import com.mega.push.cloud.config.Config;
import com.mega.push.cloud.connection.manager.ConnectionManager;
import com.mega.push.cloud.po.PushNotificationV2PO;
import com.mega.push.cloud.util.AuthTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

// import javax.annotation.PostConstruct;
import java.util.concurrent.*;

@Service
@Slf4j
public class PushUnityService {
    private final AdminService adminService;
    private final StringRedisTemplate stringRedisTemplate;
    private final ApnsService apnsService;
    private final XiaoMiService xiaoMiService;
     private final HuaWeiService huaWeiService;

    @Autowired
    public PushUnityService(AdminService adminService,
                            StringRedisTemplate stringRedisTemplate,
                            ApnsService apnsService,
                            XiaoMiService xiaoMiService, HuaWeiService huaWeiService) {
        this.adminService = adminService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.apnsService = apnsService;
        this.xiaoMiService = xiaoMiService;
        this.huaWeiService = huaWeiService;
    }

    /**
     * 统一推送
     * @param po
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void send(PushNotificationV2PO po) throws Exception {
        AuthTokenUtil.verify(po.getAuthToken(), po.getAuthSecret());
        Config config = this.adminService.getPushConfig(po.getAppId());
        ApplicationAuth applicationAuth =  config.getApplicationAuthMap().get(po.getAuthSecret());

        switch (applicationAuth.getType()) {
            case 0: {//ios
                boolean flag = true;
                for (ConfigIos configIos : config.getConfigIosList()){
                    if (configIos.getBusiId().equals(po.getMessagePO().getIosBusiId())){
                        pushIosApns(po, configIos);
                        flag = false;
                        break;
                    }
                }
                if (flag){
                    ConfigIos configIos = new ConfigIos();
                    configIos.setId(config.getConfigIosList().get(0).getId());
                    configIos.setAppId(config.getConfigIosList().get(0).getAppId());
                    configIos.setApplicationAuthId(config.getConfigIosList().get(0).getApplicationAuthId());
                    configIos.setCerPath(config.getConfigIosList().get(0).getCerPath());
                    configIos.setCerPwd(config.getConfigIosList().get(0).getCerPwd());
                    configIos.setBusiId("c");
                    pushIosApns(po,configIos);
                }
            }
            case 2: {//huawei
                if (config == null || config.getConfigAndroidList() == null) {
                    throw new RuntimeException("查询华为配置为空");
                }
                for (ConfigAndroid configAndroid : config.getConfigAndroidList()) {
                    if(configAndroid.getApplicationAuthId() != applicationAuth.getId()){
                        continue;
                    }
                    this.huaWeiService.pushHuaWeiRegIDNotification(po, configAndroid);
                }
                break;
            }
            case 3: {//xiaomi
                if (config == null || config.getConfigAndroidList() == null){
                    throw new RuntimeException("查询小米配置为空");
                }
                for (ConfigAndroid configAndroid:config.getConfigAndroidList()) {
                    // 应用和渠道都要对应
                    if(configAndroid.getApplicationAuthId() != applicationAuth.getId()){
                        continue;
                    }
                    // if (po.getMap().containsKey(configAndroid.getContentType())){
                    this.xiaoMiService.pushXiaoMiRegIDNotification(po,configAndroid);
                    // }
                }
                break;
            }
            default:
                log.error("pushUntiyEntrance :: pushUntiyEntrance");
                break;
        }
    }

    


    /**
     * 发送ios推送（构造发送消息，并存入队列）
     * @param po
     * @param configIos
     * @throws InterruptedException
     */
    public void pushIosApns(PushNotificationV2PO po, ConfigIos configIos) throws  InterruptedException{
        String appIdAUthId = configIos.getAppId()+"."+configIos.getApplicationAuthId();
        if (!ConnectionManager.apnsClientMap.containsKey(appIdAUthId) ||
                ConnectionManager.apnsClientMap.get(appIdAUthId) == null) {
            ConnectionManager.createApnsClient(configIos);
        }

        String appIdBusiId = configIos.getAppId()+"."+configIos.getBusiId();
        if (!ConnectionManager.pushNotificationMap.containsKey(appIdBusiId)
                || ConnectionManager.pushNotificationMap.get(appIdBusiId) == null){
            ConnectionManager.pushNotificationMap.put(appIdBusiId,new ConcurrentLinkedDeque<>());
            ConnectionManager.apnsAppMap.put(configIos);
        }
        this.apnsService.createApnsPushNotificationV2(po, configIos);
    }

    
    // public void googleFcmHttps(PushNotificationPO po, ConfigAndroid configAndroid) throws JsonProcessingException {
    //     Map params = new ConcurrentHashMap();
    //     Map notification = new ConcurrentHashMap();
    //     notification.put("t", LocalDateTime.now().plusHours(1));
    //     notification.put("all", 1);

    //     for(String key:po.getMap().keySet()){
    //         if (key.equals("alert")){
    //             notification.put("alert", String.valueOf(po.getMap().get(key)));
    //             notification.put("body", String.valueOf(po.getMap().get(key)));
    //         }
    //     }

    //     params.put("notification", notification);
    //     List<String> tokens = new ArrayList<>();

    //     for (String token : po.getDeviceList()) {
    //         tokens.add(token);
    //         if (tokens.size() > 49) {
    //             params.put("registration_ids", tokens);
    //             tokens.clear();
    //         }
    //     }
    //     if (tokens.size() > 0) {
    //         params.put("registration_ids", tokens);
    //         HttpConnection.pushFcmNotification(JsonUtils.toJson(params), configAndroid);
    //     }
    // }
}
