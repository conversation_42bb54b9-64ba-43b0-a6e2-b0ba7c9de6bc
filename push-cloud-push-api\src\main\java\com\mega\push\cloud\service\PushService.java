package com.mega.push.cloud.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mega.push.cloud.PushException;
import com.mega.push.cloud.common.entity.ApplicationAuth;
import com.mega.push.cloud.common.entity.ConfigAndroid;
import com.mega.push.cloud.common.entity.ConfigIos;
import com.mega.push.cloud.config.Config;
import com.mega.push.cloud.connection.HttpConnection;
import com.mega.push.cloud.connection.manager.ConnectionManager;
import com.mega.push.cloud.core.exception.BaseException;
import com.mega.push.cloud.core.utils.JsonUtils;
import com.mega.push.cloud.po.PushNotificationPO;
import com.mega.push.cloud.util.AuthTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

// import javax.annotation.PostConstruct;
import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

@Service
@Slf4j
public class PushService {
    private final AdminService adminService;
    // private final StringRedisTemplate stringRedisTemplate;
    private final ApnsService apnsService;
    // private final XiaoMiService xiaoMiService;
    // private final HuaWeiService huaWeiService;

    @Autowired
    public PushService(AdminService adminService,
                    //    StringRedisTemplate stringRedisTemplate,
                       ApnsService apnsService) {
        this.adminService = adminService;
        // this.stringRedisTemplate = stringRedisTemplate;
        this.apnsService = apnsService;
        // this.xiaoMiService = xiaoMiService;
        // this.huaWeiService = huaWeiService;
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushUntiyEntrance(PushNotificationPO po) throws Exception {
        AuthTokenUtil.verify(po.getAuthToken(), po.getAuthSecret());
        Config config = this.adminService.getPushConfig(po.getAppId());
        ApplicationAuth applicationAuth =  config.getApplicationAuthMap().get(po.getAuthSecret());

        switch (applicationAuth.getType()) {
            case 0: {//ios
                boolean flag = true;
                for (ConfigIos configIos : config.getConfigIosList()){
                    if (po.getMap().containsKey(configIos.getBusiId())){
                        pushIosApns(po,configIos);
                        flag = false;
                        break;
                    }
                }
                if (flag){
                    ConfigIos configIos = new ConfigIos();
                    configIos.setId(config.getConfigIosList().get(0).getId());
                    configIos.setAppId(config.getConfigIosList().get(0).getAppId());
                    configIos.setApplicationAuthId(config.getConfigIosList().get(0).getApplicationAuthId());
                    configIos.setCerPath(config.getConfigIosList().get(0).getCerPath());
                    configIos.setCerPwd(config.getConfigIosList().get(0).getCerPwd());
                    configIos.setBusiId("c");
                    pushIosApns(po,configIos);
                }
            }
                break;
//            case 2: {//huawei
//                if (config == null || config.getConfigAndroidList() == null){
//                    break;
//                }
//                for (ConfigAndroid configAndroid:config.getConfigAndroidList()) {
//                    if (po.getMap().containsKey(configAndroid.getContentType())){
//                        this.huaWeiService.createHuaWeiPushNotification(po,configAndroid);
//                    }
//                }
//                break;
            // case 3: {//xiaomi
            //     if (config == null || config.getConfigAndroidList() == null){
            //         throw new RuntimeException("查询小米配置为空");
            //     }
            //     for (ConfigAndroid configAndroid:config.getConfigAndroidList()) {
            //         // 应用和渠道都要对应
            //         if(configAndroid.getApplicationAuthId() != applicationAuth.getId()){
            //             continue;
            //         }
            //         // if (po.getMap().containsKey(configAndroid.getContentType())){
            //         this.xiaoMiService.pushXiaoMiRegIDNotification(po,configAndroid);
            //         // }
            //     }
            //     break;
            // }
            default:
                log.error("android推送迁移到PushUnityService");
                break;
        }
    }

    /**
     * 构造ios通知
     * @param po
     * @param configIos
     * @throws InterruptedException
     */
    public void pushIosApns(PushNotificationPO po,ConfigIos configIos) throws  InterruptedException{
        String appIdAuthId = configIos.getAppId()+"."+configIos.getApplicationAuthId();
        if (!ConnectionManager.apnsClientMap.containsKey(appIdAuthId) ||
                ConnectionManager.apnsClientMap.get(appIdAuthId) == null) {
            ConnectionManager.createApnsClient(configIos);
        }

        String appIdBusiId = configIos.getAppId()+"."+configIos.getBusiId();
        if (!ConnectionManager.pushNotificationMap.containsKey(appIdBusiId)
                || ConnectionManager.pushNotificationMap.get(appIdBusiId) == null){
            ConnectionManager.pushNotificationMap.put(appIdBusiId,new ConcurrentLinkedDeque<>());
            ConnectionManager.apnsAppMap.put(configIos);
        }
        this.apnsService.createApnsPushNotification(po, configIos);
    }

}
