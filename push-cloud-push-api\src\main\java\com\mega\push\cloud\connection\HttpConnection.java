package com.mega.push.cloud.connection;

import com.mega.push.cloud.common.entity.ConfigAndroid;
import com.mega.push.cloud.config.Config;
import com.mega.push.cloud.connection.manager.ConnectionManager;
import okhttp3.*;
import org.apache.http.HttpHeaders;

import java.io.IOException;

public class HttpConnection {
    public static void pushFcmNotification(String params, ConfigAndroid configAndroid) {
        RequestBody body = RequestBody.create(
                MediaType.parse(configAndroid.getContentType()), params);
        Request request = new Request.Builder()
                .url(configAndroid.getPushUrl())
                .addHeader(HttpHeaders.AUTHORIZATION,configAndroid.getClientSecret())
                .post(body)
                .build();
        Call call = ConnectionManager.getConnectionManager().newCall(request);
        //1.异步请求，通过接口回调告知用户 http 的异步执行结果
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                System.out.println(e.getMessage());
                call.cancel();
//                okHttpClient.clone();
            }
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    System.out.println(response.body().string());
                }
                response.close();
                call.cancel();
//                okHttpClient.clone();
            }
        });
    }

}
