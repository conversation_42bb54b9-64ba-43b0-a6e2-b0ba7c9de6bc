FROM openjdk:8-alpine
ARG JAR_FILE
ENV MEM 512m
ENV CONFIG ""
WORKDIR /opt
RUN sed -i "s/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g" /etc/apk/repositories
RUN apk add tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata
# 复制证书目录，保持完整的目录结构
COPY cerpath/ ./cerpath/
# 复制lib目录中的JAR文件到容器中，确保system scope依赖可用
COPY push-cloud-push-api/src/main/java/lib/*.jar ./lib/
ADD push-cloud-push-api/target/${JAR_FILE} app.jar
ENTRYPOINT java -Xms${MEM} -Xmx${MEM} -cp "app.jar:lib/*" org.springframework.boot.loader.JarLauncher ${CONFIG}