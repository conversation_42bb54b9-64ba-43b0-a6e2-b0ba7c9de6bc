<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.push.cloud.dao.AdminDao">
    
    <!-- 根据应用密钥和密钥获取应用授权信息 -->
    <select id="getApplicationAuthByKeyAndSecret" resultType="com.mega.push.cloud.common.entity.ApplicationAuth">
        SELECT 
            *
        FROM 
            application_auth
        WHERE 
            appkey = #{appKey} 
            AND appscret = #{appSecret}
            AND delsign = 0
    </select>
    
    <!-- 根据ID获取应用信息 -->
    <select id="getApplicationInfoById" resultType="com.mega.push.cloud.common.entity.ApplicationInfo">
        SELECT 
           *
        FROM 
            application_info
        WHERE 
            id = #{id}
            AND delsign = 0
    </select>
    
    <!-- 获取所有应用信息 -->
    <select id="getAllApplicationInfo" resultType="com.mega.push.cloud.common.entity.ApplicationInfo">
        SELECT 
            *
        FROM 
            application_info
        WHERE 
            delsign = 0
    </select>
    
    <!-- 根据应用ID获取应用授权列表 -->
    <select id="getApplicationAuthByAppId" resultType="com.mega.push.cloud.common.entity.ApplicationAuth">
        SELECT 
         *
        FROM 
            application_auth
        WHERE 
            application_info_id = #{applicationInfoId}
            AND delsign = 0
    </select>
    
    <!-- 根据授权ID获取iOS配置 -->
    <select id="getConfigIosByAuthId" resultType="com.mega.push.cloud.common.entity.ConfigIos">
        SELECT 
            *
        FROM 
            config_ios
        WHERE 
            application_auth_id = #{applicationAuthId}
    </select>
    
    <!-- 根据授权ID获取Android配置 -->
    <select id="getConfigAndroidByAuthId" resultType="com.mega.push.cloud.common.entity.ConfigAndroid">
        SELECT 
         *
        FROM 
            config_android
        WHERE 
            application_auth_id = #{applicationAuthId}
    </select>
    
</mapper>