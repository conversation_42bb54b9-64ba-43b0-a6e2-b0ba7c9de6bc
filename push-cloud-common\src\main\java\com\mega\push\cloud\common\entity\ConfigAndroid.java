package com.mega.push.cloud.common.entity;

import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "config_android")
public class ConfigAndroid {
    /**
     * 应用ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Integer id;

    @Column(name = "application_auth_id")
    private Long applicationAuthId;

    /**
     * 渠道应用id
     */
    @Column(name = "client_id")
    private String clientId;

    /**
     * 应用密钥
     */
    @Column(name = "client_key")
    private String clientKey;

    /**
     * 应用密钥
     */
    @Column(name = "client_secret")
    private String clientSecret;

    /**
     * 推送请求类型
     */
    @Column(name = "content_type")
    private String contentType;

    /**
     * 应用包名
     */
    @Column(name = "package_name")
    private String packageName;

    /**
     * 标注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 获取token URL
     */
    @Column(name = "token_url")
    private String tokenUrl;

    /**
     * 推送URL
     */
    @Column(name = "push_url")
    private String pushUrl;
}