spring:
  cloud:
    consul:
      enabled: false
      host: *************
      port: 8500
      discovery:
        prefer-ip-address: true
        ip-address: *************
        instance-id: ${spring.application.name}-${server-port}
        service-name: ${spring.application.name}
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    username: admin
    password: Mangosteen0!
    maximum-pool-size: 10
  redis:
    # 添加默认 Redis 配置
    host: **************
    port: 6379
    password: LA1954b!
    database: 0
    # 保留自定义配置
    db0:
      host: **************
      port: 6379
      password: LA1954b!
      database: 0
    db3:
      host: **************
      port: 6379
      password: LA1954b!
      database: 3
    db8:
      host: **************
      port: 6379
      password: LA1954b!
      database: 8

