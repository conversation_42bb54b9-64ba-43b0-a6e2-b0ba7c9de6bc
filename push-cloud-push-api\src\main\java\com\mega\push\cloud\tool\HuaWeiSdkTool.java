package com.mega.push.cloud.tool;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.huawei.push.android.*;
import com.huawei.push.exception.HuaweiMesssagingException;
import com.huawei.push.message.AndroidConfig;
import com.huawei.push.message.Message;
import com.huawei.push.message.Notification;
import com.huawei.push.messaging.HuaweiApp;
import com.huawei.push.messaging.HuaweiMessaging;
import com.huawei.push.reponse.SendResponse;
import com.huawei.push.util.InitAppUtils;
import com.mega.push.cloud.core.utils.JsonUtils;
import com.mega.push.cloud.po.PushMessagePO;
import com.xiaomi.push.sdk.ErrorCode;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class HuaWeiSdkTool {
    public static SendResponse sendNotification(String appId, String appSecret, List<String> tokenList, PushMessagePO pushMessagePO) throws HuaweiMesssagingException, JsonProcessingException {
        HuaweiApp app = InitAppUtils.initializeApp(appId, appSecret);
        HuaweiMessaging huaweiMessaging = HuaweiMessaging.getInstance(app);

        Notification notification = Notification.builder().setTitle(pushMessagePO.getTitle())
                .setBody(pushMessagePO.getDescription())
                .build();

        ClickAction clickAction;
        switch (pushMessagePO.getAndroidActivityType()) {
            // 消息点击行为类型，取值如下：
            //1：打开应用自定义页面
            //2：点击后打开特定URL
            //3：点击后打开应用
            case 1:{
                clickAction = ClickAction.builder().setType(3).build();
                break;
            }
            case 2:{
                clickAction = ClickAction.builder().setType(1).setIntent(pushMessagePO.getAndroidActivityUri()).build();
                break;
            }
            case 3:{
                clickAction = ClickAction.builder().setType(2).setUrl(pushMessagePO.getAndroidActivityUri()).build();
                break;
            }
            default:
                clickAction = ClickAction.builder().setType(3).build();
                break;
        }
        AndroidNotification androidNotification = AndroidNotification.builder()
                .setClickAction(clickAction)
                .build();

        AndroidConfig androidConfig = AndroidConfig.builder()
                .setNotification(androidNotification)
                .build();

        Message message = Message.builder().setNotification(notification)
                .setAndroidConfig(androidConfig)
                .addAllToken(tokenList)
                .build();


        SendResponse response = huaweiMessaging.sendMessage(message);
        if(response.getCode().equals("80000000")){   //发送成功   发送失败会返回错误码
            log.info("HuaWei Push Success! : ");
        }else{
            log.error("HuaWei Push Error SendResponse : "+ JsonUtils.toJson(response));   //发送失败会返回错误码
        }
        return response;
    }
}
