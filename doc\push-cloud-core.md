# push-cloud-core 模块文档

## 概述
push-cloud-core 模块包含项目的核心功能，如结果处理、全局异常处理、认证、上下文管理和工具类。

## 主要功能
- **Result.java**: 结果类。
- **ResultCode.java**: 结果代码类。
- **Results.java**: 结果工具类。
- **GlobalExceptionHandler.java**: 全局异常处理器。
- **Authorizing.java**: 认证接口。
- **AuthorizingAspect.java**: 认证切面。
- **AuthorizingRealm.java**: 认证领域。
- **UserRealm.java**: 用户领域。
- **SpringApplicationContext.java**: Spring应用上下文工具类。
- **BaseException.java**: 基础异常类。
- **HttpUtils.java**: HTTP工具类。
- **JsonUtils.java**: JSON工具类。
- **SpringUtils.java**: Spring工具类。
- **UUIDUtils.java**: UUID工具类。

## 使用方法
请参考源代码中的注释和文档。