package com.mega.push.cloud.config;

import com.mega.push.cloud.common.entity.ApplicationAuth;
import com.mega.push.cloud.common.entity.ConfigAndroid;
import com.mega.push.cloud.common.entity.ConfigIos;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class Config {
    private Map<String, ApplicationAuth> applicationAuthMap;
    private List<ConfigIos> configIosList;
    private List<ConfigAndroid> configAndroidList;
}
