logging:
  file:
    path: /opt/log/${spring.application.name}-${server.port}/
    max-size: 128MB
spring:
  cloud:
    consul:
      enabled: true
      host: ************
      port: 8500
      discovery:
        prefer-ip-address: true
        ip-address: *************
        instance-id: ${spring.application.name}-${server.port}
        service-name: ${spring.application.name}
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    username: pushCloud
    password: Mangosteen0!
    maximum-pool-size: 10
  redis:
    db0:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26379
          - *************:26379
      database: 0
      password: LA1954b!
    db3:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26379
          - *************:26379
      database: 3
      password: LA1954b!
    db8:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26379
          - *************:26379
      database: 8
      password: LA1954b!
    db14:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26379
          - *************:26379
      database: 14
      password: LA1954b!