package com.mega.push.cloud.controller;

import com.mega.push.cloud.core.Result;
import com.mega.push.cloud.core.Results;
import com.mega.push.cloud.po.AuthPO;
import com.mega.push.cloud.service.AdminService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.text.ParseException;

@Api(tags = "管理接口")
@RestController
public class AdminController {

    private final AdminService adminService;

    @Autowired
    public AdminController(AdminService adminService) {
        this.adminService = adminService;
    }

    @ApiOperation("获取authToken")
    @ApiResponses({
            @ApiResponse(code = 900, message = "领取失败"),
    })
    @PostMapping("/admin/auth/token")
    public Result<?> tiroSignAward(@Validated @RequestBody AuthPO po) throws IOException, InterruptedException {
        return Results.success(this.adminService.auth(po));
    }

    @ApiOperation("第二版获取authToken")
    @ApiResponses({
            @ApiResponse(code = 900, message = "领取失败"),
    })
    @PostMapping("/push/api/auth/token")
    public Result<?> tiroSignAwardVPN(@Validated @RequestBody AuthPO po) throws IOException, InterruptedException {
        return Results.success(this.adminService.auth(po));
    }

}
