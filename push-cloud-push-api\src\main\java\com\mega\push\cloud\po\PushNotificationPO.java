package com.mega.push.cloud.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class PushNotificationPO {
    @ApiModelProperty("设备token数组")
    @NotNull
    private List<String> deviceList;

    @ApiModelProperty("应用id")
    @NotNull
    private Long appId;


    @ApiModelProperty("权限密钥")
    @NotNull
    private String authSecret;

    @ApiModelProperty("权限令牌")
    @NotNull
    private String authToken;
    
    @ApiModelProperty("其他参数")
    @NotNull
    private Map<String, String> map;
}
