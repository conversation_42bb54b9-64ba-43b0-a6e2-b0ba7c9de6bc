package com.mega.push.cloud.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mega.push.cloud.PushCode;
import com.mega.push.cloud.PushException;
import com.mega.push.cloud.common.entity.ApplicationAuth;
import com.mega.push.cloud.core.utils.JsonUtils;
import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class AuthTokenUtil {
    private static Key aesKey = getAESKey();

    /**
     * 校验token，主要是校验两个方面
     * 1、校验签名是否合法
     * 2、校验token是否过期
     *
     * @param token
     * @return
     */
    public static boolean verify(String token, String hmacKey) throws Exception {

        String[] list = token.split("\\.");
        if (list.length != 3) {
            throw new PushException(PushCode.PUSH_TOKEN_LENGHT_ERROR);
        }
        String encryptJson = list[1];
        String sign = list[2];
        String headerJson = list[0];
        if (!verifySign(encryptJson, sign, hmacKey)) {
            throw new PushException(PushCode.PUSH_TOKEN_INVALID_ERROR);
        }

        if (!verifyExpire(headerJson)) {
            throw new PushException(PushCode.PUSH_TOKEN_TIMEOUT_ERROR);
        }
        return true;
    }

    /**
     * 校验token是否过期
     *
     * @param headerJson 请求头的base64编码
     * @return
     */
    @SneakyThrows
    private static boolean verifyExpire(String headerJson) {
        byte[] headerBytes = base64Decoder(headerJson);
        String json = new String(headerBytes);
        Map<String, Object> map = JsonUtils.fromJson(json, new TypeReference<Map<String, Object>>() {
        });
        long expireTime = ((Number) map.get("expire")).longValue();
        return expireTime > System.currentTimeMillis() / 1000;
    }

    /**
     * 校验签名  使用base64解码后的字节数组来生成签名，并与传递来的签名进行比较得出合法性
     *
     * @param encryptJson
     * @param sign
     * @return
     * @throws Exception
     */
    private static boolean verifySign(String encryptJson, String sign, String hmacKey) throws Exception {
        byte[] encryptBody = base64Decoder(encryptJson);
        byte[] bytes = generateHmacSign(encryptBody, hmacKey);
        return StringUtils.equals(sign, base64Encoder(bytes));
    }

    private static byte[] AESDecryptBody(byte[] encryptBody) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, aesKey);
        return cipher.doFinal(encryptBody);
    }

    /**
     * @param tokenBody 实例对象，通常为bean
     * @param minute    过期时间   单位:min
     * @return
     */
    public static String createToken(ApplicationAuth tokenBody, int minute) throws JsonProcessingException {
        long now = System.currentTimeMillis() / 1000;
        String jsonBody = "{\"body\":" + JsonUtils.toJson(tokenBody) + "}";

        String randomAlphabetic = RandomStringUtils.randomAlphabetic(3);
        Map<String, Object> jsonHeader = new ConcurrentHashMap<>();
        jsonHeader.put("now", now);
        jsonHeader.put("rand_num", randomAlphabetic);
        jsonHeader.put("expire", (now + minute * 60)); // 过期时间 单位：秒

        String token = null;
        try {
            byte[] encryptContent = generateEncryptBody(JsonUtils.toJson(jsonHeader), jsonBody);
            byte[] signWithEncrypt = generateSignWithEncrypt(encryptContent, tokenBody.getAppscret());

            StringJoiner joiner = new StringJoiner(".");
            for (String str : new String[]{base64Encoder(
                    JsonUtils.toJson(jsonHeader).getBytes("utf-8")),
                    base64Encoder(encryptContent),
                    base64Encoder(signWithEncrypt)}) {
                joiner.add(str);
            }
            token = joiner.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return token;
    }

    private static byte[] generateEncryptBody(String header, String body) throws Exception {
        byte[] headerBytes = header.getBytes("utf-8");
        byte[] bodyBytes = body.getBytes("utf-8");
        byte[] content = xor(headerBytes, bodyBytes);
        byte[] encryptContent = AESEncrypt(content);
        return encryptContent;
    }

    private static byte[] generateSignWithEncrypt(byte[] encryptContent, String hmacKey) throws Exception {
        byte[] result = generateHmacSign(encryptContent, hmacKey);
        return result;
    }

    private static byte[] generateHmacSign(byte[] content, String hmacKey) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(hmacKey.getBytes("utf-8"), "HmacMD5");
        Mac mac = Mac.getInstance("HmacMD5");
        mac.init(secretKey);
        return mac.doFinal(content);
    }

    private static String base64Encoder(byte[] data) {
        return Base64.encodeBase64String(data);
    }

    private static byte[] base64Decoder(String content) {
        return Base64.decodeBase64(content);
    }

    private static byte[] xor(byte[] header, byte[] body) {
        byte[] xorData = new byte[body.length];
        for (int i = 0; i < body.length; i++) {
            int idx = i % header.length;
            xorData[i] = (byte) (body[i] ^ header[idx]);
        }
        return xorData;
    }

    private static Key getAESKey() {
        Key key = null;
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
            keyGenerator.init(new SecureRandom());
            byte[] encodedKey = keyGenerator.generateKey().getEncoded();
            key = new SecretKeySpec(encodedKey, "AES");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return key;
    }

    private static byte[] AESEncrypt(byte[] content) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, aesKey);
        return cipher.doFinal(content);
    }

    private static byte[] AESDecrypt(byte[] content) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, aesKey);
        return cipher.doFinal(content);
    }

    @SneakyThrows
    public static void main(String[] args) throws InterruptedException {
        String token = createToken(new ApplicationAuth(), 1);
        System.out.println(token);
        System.out.println(verify(token, ""));
        System.out.println(verify(token + "ab", ""));
        Thread.sleep(70000);
        System.out.println(verify(token, ""));
    }
}

