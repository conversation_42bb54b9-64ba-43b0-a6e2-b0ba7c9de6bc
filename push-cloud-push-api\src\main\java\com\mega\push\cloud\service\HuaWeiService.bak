package com.mega.push.cloud.service;


import com.alibaba.fastjson.JSONObject;
import com.huawei.push.android.*;
import com.huawei.push.message.AndroidConfig;
import com.huawei.push.message.Message;
import com.huawei.push.message.Notification;
import com.huawei.push.messaging.HuaweiApp;
import com.huawei.push.messaging.HuaweiCredential;
import com.huawei.push.messaging.HuaweiMessaging;
import com.huawei.push.messaging.HuaweiOption;
import com.huawei.push.model.Importance;
import com.huawei.push.model.Urgency;
import com.huawei.push.model.Visibility;
import com.huawei.push.reponse.SendResponse;
import com.mega.push.cloud.common.entity.ConfigAndroid;
import com.mega.push.cloud.common.entity.PushLogger;
import com.mega.push.cloud.common.mapper.BadTokenMapper;
import com.mega.push.cloud.common.mapper.PushLoggerMapper;
import com.mega.push.cloud.core.utils.JsonUtils;
import com.mega.push.cloud.po.PushNotificationPO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class HuaWeiService {

    private final PushLoggerMapper pushLoggerMapper;
    private final BadTokenMapper badTokenMapper;
    private final LightSettings lightSettings = LightSettings.builder().setColor(Color.builder().setAlpha(0f).setRed(0f).setBlue(1f).setGreen(1f).build())
            .setLightOnDuration("3.5")
            .setLightOffDuration("5S")
            .build();

    @Autowired
    public HuaWeiService(PushLoggerMapper pushLoggerMapper, BadTokenMapper badTokenMapper) {
        this.pushLoggerMapper = pushLoggerMapper;
        this.badTokenMapper = badTokenMapper;
    }


    @SneakyThrows
    public void createHuaWeiPushNotification(PushNotificationPO po, ConfigAndroid configAndroid){
        PushLogger pushLogger = new PushLogger();
        pushLogger.setPushApns(JsonUtils.toJson(po.getMap()));
        pushLogger.setAppId(po.getAppId().toString());
        pushLogger.setCreatetime(new Date());
        pushLogger.setBusiId(configAndroid.getContentType());
        this.pushLoggerMapper.insert(pushLogger);
        HuaweiCredential credential = HuaweiCredential.builder()
                .setAppId(configAndroid.getClientKey())
                .setAppSecret(configAndroid.getClientSecret())
                .build();
        HuaweiOption option = HuaweiOption.builder()
                .setCredential(credential)
                .build();
        HuaweiApp app = HuaweiApp.getInstance(option);
        HuaweiMessaging huaweiMessaging = HuaweiMessaging.getInstance(app);

        Notification notification = Notification.builder().setTitle(po.getMap().get("title"))
                .setBody(po.getMap().get("alert"))
                .build();

        JSONObject multiLangKey = new JSONObject();
        for (Map.Entry<String, String> entry:po.getMap().entrySet()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("en",entry.getValue());
            multiLangKey.put(entry.getKey(),jsonObject);
        }

        AndroidNotification androidNotification = AndroidNotification.builder()
                .setMultiLangkey(multiLangKey)
                .setStyle(1)
                .setClickAction(ClickAction.builder().setType(3).build())
                .setBigTitle(po.getMap().get("title"))
                .setBigBody(po.getMap().get("alert"))
                .setAutoClear(86400000)
                .setNotifyId(2)
                .setImportance(Importance.LOW.getValue())
                .setLightSettings(lightSettings)
                .setBadge(BadgeNotification.builder().setAddNum(1).setBadgeClass("Classic").build())
                .setVisibility(Visibility.PUBLIC.getValue())
                .setForegroundShow(true)
                .build();

        AndroidConfig androidConfig = AndroidConfig.builder().setCollapseKey(-1)
                .setUrgency(Urgency.HIGH.getValue())
                .setTtl("10000s")
                .setBiTag("the_sample_bi_tag_for_receipt_service")
                .setNotification(androidNotification)
                .build();
        List<String> tokenList = new ArrayList<>();

        for (String deviceId :po.getDeviceList()) {
            if (tokenList.size()>99){
                Message message = Message.builder().setNotification(notification)
                        .setAndroidConfig(androidConfig)
                        .addAllToken(tokenList)
                        .build();
                SendResponse response = huaweiMessaging.sendMessage(message);
                log.info("SendResponse : "+JsonUtils.toJson(response));
                tokenList.clear();
            }
            tokenList.add(deviceId);
        }
        if (tokenList.size()>0){
            Message message = Message.builder().setNotification(notification)
                    .setAndroidConfig(androidConfig)
                    .addAllToken(po.getDeviceList())
                    .build();
            SendResponse response = huaweiMessaging.sendMessage(message);
            log.info("SendResponse : "+JsonUtils.toJson(response));
        }
        PushLogger pushLogger1 = new PushLogger();
        pushLogger1.setId(pushLogger.getId());
        pushLogger1.setEndtime(new Date());
        this.pushLoggerMapper.updateByPrimaryKeySelective(pushLogger1);
    }


    @Async("taskExecutor")
    public void createHuaWeiPushSender(String appId,String busiId){
        log.info(appId+" : thread start");
        while (true){
            try{

            }catch (Exception e){
                log.error("Exception : "+ e.toString());
            }
        }
    }

}
