package com.mega.push.cloud.service;

import com.huawei.push.reponse.SendResponse;
import com.mega.push.cloud.common.entity.ConfigAndroid;
import com.mega.push.cloud.common.entity.PushLogger;
import com.mega.push.cloud.common.mapper.PushLoggerMapper;
import com.mega.push.cloud.core.utils.JsonUtils;
import com.mega.push.cloud.po.PushNotificationV2PO;
import com.mega.push.cloud.tool.HuaWeiSdkTool;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class HuaWeiService {
    private final PushLoggerMapper pushLoggerMapper;

    public HuaWeiService(PushLoggerMapper pushLoggerMapper) {
        this.pushLoggerMapper = pushLoggerMapper;
    }

    @SneakyThrows
    public void pushHuaWeiRegIDNotification(PushNotificationV2PO po, ConfigAndroid configAndroid){
        // 1 推送日志
        PushLogger pushLogger = new PushLogger();
        pushLogger.setPushApns(JsonUtils.toJson(po.getMessagePO()));
        pushLogger.setAppId(po.getAppId().toString());
        pushLogger.setCreatetime(new Date());
        pushLogger.setBusiId(configAndroid.getContentType());
        this.pushLoggerMapper.insert(pushLogger);

        // 2 推送内容检测
        // 设置在通知栏展示的通知的标题, 不允许全是空白字符, 长度小于50, 一个中英文字符均计算为1(通知栏消息必填)
        if (po.getMessagePO().getTitle() == null){
            return;
        }
        // 设置在通知栏展示的通知描述, 不允许全是空白字符, 长度小于128, 一个中英文字符均计算为1(通知栏消息必填)
        if (po.getMessagePO().getDescription() == null){
            return;
        }

        // 3发送推送
        SendResponse result = HuaWeiSdkTool.sendNotification(configAndroid.getClientId(), configAndroid.getClientSecret(), po.getDeviceList(), po.getMessagePO());
        // 4 推送成功 更新日志
        if (result.getCode().equals("80000000")) {
            PushLogger pushLogger1 = new PushLogger();
            pushLogger1.setId(pushLogger.getId());
            pushLogger1.setEndtime(new Date());
            this.pushLoggerMapper.updateByPrimaryKeySelective(pushLogger1);
        }
    }
}
