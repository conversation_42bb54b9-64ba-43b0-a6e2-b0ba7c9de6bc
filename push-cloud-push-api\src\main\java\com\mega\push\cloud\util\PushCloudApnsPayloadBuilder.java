package com.mega.push.cloud.util;

import com.eatthepath.json.JsonSerializer;
import com.eatthepath.pushy.apns.util.ApnsPayloadBuilder;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class PushCloudApnsPayloadBuilder extends ApnsPayloadBuilder {

    private Map<String, Object> apsOtherKey = new ConcurrentHashMap<>();

    public ApnsPayloadBuilder addApsOtherKey(final String key, final Object value) {
        this.apsOtherKey.put(key, value);
        return this;
    }

    @Override
    public String build() {
        return JsonSerializer.writeJsonTextAsString(this.buildPayloadMap());
    }

    @Override
    public String buildMdmPayload(final String pushMagicValue) {
        return JsonSerializer.writeJsonTextAsString(this.buildMdmPayloadMap(pushMagicValue));
    }
    @Override
    public Map<String, Object> buildPayloadMap(){
        Map<String, Object> payload = super.buildPayloadMap();
        Map<String, Object> aps = (Map<String, Object>) payload.get("aps");
        for (final Map.Entry<String, Object> entry : this.apsOtherKey.entrySet()) {
            aps.put(entry.getKey(), entry.getValue());
        }
        payload.put("aps",aps);
        return payload;
    }
}
