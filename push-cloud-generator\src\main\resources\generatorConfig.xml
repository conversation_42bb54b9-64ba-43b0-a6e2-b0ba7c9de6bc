<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="Mysql" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="javaFileEncoding" value="UTF-8"/>

        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="tk.mybatis.mapper.common.Mapper"/>
            <property name="caseSensitive" value="true"/>
            <property name="forceAnnotation" value="true"/>
            <property name="beginningDelimiter" value=""/>
            <property name="endingDelimiter" value=""/>
            <property name="lombok" value="Getter,Setter,Accessors"/>
        </plugin>

        <plugin type="tk.mybatis.mapper.generator.TemplateFilePlugin">
            <property name="targetProject" value="../push-cloud-common/src/main/java"/>
            <property name="targetPackage" value="com.mega.push.cloud.common.mapper"/>
            <property name="templatePath" value="mapper.ftl"/>
            <property name="mapperSuffix" value="Mapper"/>
            <property name="fileName" value="${tableClass.shortClassName}${mapperSuffix}.java"/>
        </plugin>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="********************************************"
                        userId="admin"
                        password="Mangosteen0!">
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.mega.push.cloud.common.entity"
                            targetProject="../push-cloud-common/src/main/java">
        </javaModelGenerator>

        <table tableName="application_info">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="application_auth">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="config_android">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="config_ios">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="push_logger">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
        <table tableName="bad_token">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
    </context>
</generatorConfiguration>
