package com.mega.push.cloud.tool;

// import com.xiaomi.push.sdk.ErrorCode;
// import com.xiaomi.xmpush.server.Result;
// import com.xiaomi.xmpush.server.Sender;
import com.mega.push.cloud.po.PushMessagePO;
import com.xiaomi.xmpush.server.Result;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PrepareForTest;
// import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PowerMockIgnore;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.reflect.Whitebox;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//import static org.junit.Assert.assertEquals;
//import static org.junit.Assert.assertNotNull;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyInt;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.when;

/**
 * XiaoMiSdkTool单元测试类
 */

@PrepareForTest({XiaoMiSdkTool.class})
//@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
public class XiaoMiSdkToolTest {

    private static final String APP_SECRET_KEY = "695upxOHm16xXa5VqT6nIA==";
    private static final String PACKAGE_NAME = "com.gradient.vpn";
    private static final String title = "Test Title";
    private static final String descirption = "Test Description";
    private static final String pauload = "{\"key\":\"value\"}";

    // private Sender mockSender;
    // private Result mockResult;

    // @Before
    // public void setUp() throws Exception {
    //     // 创建模拟对象
    //     mockSender = mock(Sender.class);
    //     mockResult = mock(Result.class);

    //     // 设置模拟行为
    //     when(mockResult.getErrorCode()).thenReturn(ErrorCode.Success);
    //     when(mockSender.send(any(), any(List.class), anyInt())).thenReturn(mockResult);

    //     // 使用PowerMockito来模拟静态字段
    //     Map<String, Sender> senderMap = new HashMap<>();
    //     Whitebox.setInternalState(XiaoMiSdkTool.class, "senderMap", senderMap);
    // }

    /**
     * 测试首次调用sendRegPush方法时创建新的Sender
     */
    @Test
    public void testSendRegPushWithNewSender() throws Exception {
        // 准备测试数据
        List<String> regIdList = new ArrayList<>();
        regIdList.add("AmLqWIIij0bZVPNXhVH+yHHqHM105+M/u/m1+A6BW5HOWnecq+4RrDqYPsi3xQhw");

        PushMessagePO pushMessagePO = new PushMessagePO();
        pushMessagePO.setTitle(title);
        pushMessagePO.setDescription(descirption);
        pushMessagePO.setPayload(pauload);
        pushMessagePO.setAndroidActivityType(1);
        pushMessagePO.setChannelId(136201);
        // 执行测试方法
        Result result = XiaoMiSdkTool.sendRegPush(APP_SECRET_KEY, PACKAGE_NAME, regIdList, pushMessagePO);

    }

    // /**
    //  * 测试重复调用sendRegPush方法时使用缓存的Sender
    //  */
    // @Test
    // public void testSendRegPushWithCachedSender() throws Exception {
    //     // 准备测试数据
    //     List<String> regIdList = new ArrayList<>();
    //     regIdList.add("test_reg_id_1");

    //     // 预先设置缓存的Sender
    //     Map<String, Sender> senderMap = Whitebox.getInternalState(XiaoMiSdkTool.class, "senderMap");
    //     senderMap.put(APP_SECRET_KEY, mockSender);

    //     // 执行测试方法
    //     Result result = XiaoMiSdkTool.sendRegPush(APP_SECRET_KEY, PACKAGE_NAME, regIdList, TITLE, DESCRIPTION, PAYLOAD);

    //     // 验证结果
    //     assertNotNull("Result should not be null", result);
    //     assertEquals("Error code should be Success", ErrorCode.Success, result.getErrorCode());

    //     // 验证没有创建新的Sender实例
    //     PowerMockito.verifyNew(Sender.class, Mockito.never()).withArguments(APP_SECRET_KEY);
    // }

    // /**
    //  * 测试发送失败的情况
    //  */
    // @Test
    // public void testSendRegPushFailure() throws Exception {
    //     // 准备测试数据
    //     List<String> regIdList = new ArrayList<>();
    //     regIdList.add("test_reg_id_1");

    //     // 模拟发送失败的情况
    //     Result failureResult = mock(Result.class);
    //     when(failureResult.getErrorCode()).thenReturn(ErrorCode.InvalidPayload);
    //     when(mockSender.send(any(), any(List.class), anyInt())).thenReturn(failureResult);

    //     // 预先设置缓存的Sender
    //     Map<String, Sender> senderMap = Whitebox.getInternalState(XiaoMiSdkTool.class, "senderMap");
    //     senderMap.put(APP_SECRET_KEY, mockSender);

    //     // 执行测试方法
    //     Result result = XiaoMiSdkTool.sendRegPush(APP_SECRET_KEY, PACKAGE_NAME, regIdList, TITLE, DESCRIPTION, PAYLOAD);

    //     // 验证结果
    //     assertNotNull("Result should not be null", result);
    //     assertEquals("Error code should be InvalidPayload", ErrorCode.InvalidPayload, result.getErrorCode());
    // }

    // /**
    //  * 测试发送时抛出IOException的情况
    //  */
    // @Test(expected = IOException.class)
    // public void testSendRegPushWithIOException() throws Exception {
    //     // 准备测试数据
    //     List<String> regIdList = new ArrayList<>();
    //     regIdList.add("test_reg_id_1");

    //     // 模拟发送时抛出IOException
    //     when(mockSender.send(any(), any(List.class), anyInt())).thenThrow(new IOException("Test exception"));

    //     // 预先设置缓存的Sender
    //     Map<String, Sender> senderMap = Whitebox.getInternalState(XiaoMiSdkTool.class, "senderMap");
    //     senderMap.put(APP_SECRET_KEY, mockSender);

    //     // 执行测试方法 - 应该抛出IOException
    //     XiaoMiSdkTool.sendRegPush(APP_SECRET_KEY, PACKAGE_NAME, regIdList, TITLE, DESCRIPTION, PAYLOAD);
    // }
}
