# PushController 接口流程图和时序图

## 旧版本推送（只实现ios）
## 第二版统一推送（实现ios、android）

### 流程图
```mermaid
graph TD
    用户 -->|请求推送消息| PushController
    PushController -->|根据平台类型发送推送消息| 推送服务
    推送服务 -->|返回推送结果| PushController
    PushController -->|返回推送结果| 用户
```

### 时序图
```mermaid
sequenceDiagram
    participant 用户
    participant PushController
    participant 推送服务
    用户 ->> PushController: 推送消息请求
    PushController ->> 推送服务: 发送推送消息
    推送服务 -->> PushController: 返回推送结果
    PushController -->> 用户: 返回推送结果