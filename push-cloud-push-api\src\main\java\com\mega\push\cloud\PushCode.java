package com.mega.push.cloud;

import com.mega.push.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum PushCode {

    PUSH_APPLICARTION_AUTH_ERROR(10001),
    PUSH_APPLICARTION_INFO_ERROR(10002),
    PUSH_APPLICARTION_TOKEN_ERROR(10003),

    PUSH_TOKEN_LENGHT_ERROR(10004),
    PUSH_TOKEN_INVALID_ERROR(10005),
    PUSH_TOKEN_TIMEOUT_ERROR(10006),
    ;

    private final Integer code;

    PushCode(Integer code) {
        this.code = code;
    }

    public static PushCode getExchangeCode(Integer code) {
        for (PushCode exchangeCode : PushCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
