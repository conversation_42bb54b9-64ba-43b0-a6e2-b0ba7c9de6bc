# Push Cloud Push API

Push Cloud Push API 是一个用于管理和发送推送通知的服务，支持多种推送平台，包括 iOS (APNS)、Android (FCM、华为、小米) 等。

## 项目结构

```
push-cloud-push-api/
├── src/main/java/com/mega/push/cloud/
│   ├── controller/           # 控制器层，处理HTTP请求
│   │   ├── AdminController.java  # 管理接口
│   │   └── PushController.java   # 推送接口
│   ├── service/              # 服务层，实现业务逻辑
│   │   ├── AdminService.java     # 管理服务
│   │   ├── ApnsService.java      # iOS推送服务
│   │   ├── HuaWeiService.java    # 华为推送服务
│   │   ├── PushService.java      # 通用推送服务
│   │   └── XiaoMiService.java    # 小米推送服务
│   ├── po/                   # 参数对象
│   │   ├── PushNotificationPO.java  # 推送通知参数对象
│   │   ├── PushNotificationV2PO.java # 推送通知参数对象V2版本
│   │   └── PushMessagePO.java    # 推送消息参数对象
│   ├── tool/                 # 工具类
│   │   └── XiaoMiSdkTool.java    # 小米推送SDK工具类
│   ├── config/               # 配置类
│   │   └── Config.java          # 推送配置
│   ├── connection/           # 连接管理
│   │   └── manager/ConnectionManager.java  # 连接管理器
│   ├── PushApiApplication.java  # 应用入口
│   ├── PushCode.java           # 错误码定义
│   └── PushConfig.java         # Swagger配置
├── src/main/resources/
│   ├── application.yml         # 通用配置
│   ├── application-dev.yml     # 开发环境配置
│   ├── application-test.yml    # 测试环境配置
│   ├── application-beta.yml    # Beta环境配置
│   ├── application-prod.yml    # 生产环境配置
│   ├── application-bj.yml      # 北京环境配置
│   ├── application-jp.yml      # 日本环境配置
│   └── i18n/                   # 国际化资源
└── Dockerfile                  # Docker构建文件
```

## 数据库结构

系统使用以下主要数据表：

- `application_info`: 应用信息表
- `application_auth`: 应用授权表
- `config_ios`: iOS配置表
- `config_android`: Android配置表
- `push_logger`: 推送日志表
- `bad_token`: 无效Token表

## 接口文档

### 管理接口

#### 获取认证令牌

```
POST /admin/auth/token
```

**请求参数**:

```json
{
  "authKey": "应用密钥",
  "authSecret": "应用密钥"
}
```

**响应**:

```json
{
  "code": 0,
  "data": {
    "appId": "应用ID",
    "appName": "应用名称",
    "authKey": "应用密钥",
    "authSecret": "应用密钥",
    "authToken": "认证令牌"
  }
}
```

### 推送接口

#### 统一推送入口

```
POST /push/unity/entrance
```

**请求参数**:

```json
{
  "deviceList": ["设备token1", "设备token2"],
  "appId": 123,
  "authSecret": "应用密钥",
  "authToken": "认证令牌",
  "map": {
    "alert": "推送内容",
    "title": "推送标题",
    "sound": "声音文件名",
    "其他自定义参数": "值"
  }
}
```

**响应**:

```json
{
  "code": 0,
  "data": null
}
```

#### Fastbird推送入口

```
POST /push/fastbird/entrance
```

**请求参数**:

```json
{
  "deviceList": ["设备token1", "设备token2"],
  "appId": 123,
  "authSecret": "应用密钥",
  "authToken": "认证令牌",
  "messagePO": {
    "title": "推送标题",
    "description": "推送内容",
    "payload": "自定义数据",
    "channelId": 136201,
    "androidActivityType": 1
  }
}
```

**响应**:

```json
{
  "code": 0,
  "data": null
}
```





参数和响应与统一推送入口相同。

## 服务方法说明

### PushService

主要负责根据应用类型分发推送请求到不同的具体推送服务。

- `pushUntiyEntrance(PushNotificationPO po)`: 统一推送入口，根据应用类型分发推送请求
- `pushIosApns(PushNotificationPO po, ConfigIos configIos)`: 处理iOS推送
- `googleFcmHttps(PushNotificationPO po, ConfigAndroid configAndroid)`: 处理FCM推送

### ApnsService

处理iOS APNS推送。

- `createApnsPushNotification(PushNotificationPO po, ConfigIos configIos)`: 创建并发送iOS推送
- `appleTlsHttp2(String appId, String busiId)`: 异步处理iOS推送队列

### HuaWeiService

处理华为推送。

- `createHuaWeiPushNotification(PushNotificationPO po, ConfigAndroid configAndroid)`: 创建并发送华为推送
- `createHuaWeiPushSender(String appId, String busiId)`: 异步处理华为推送队列

### XiaoMiService

处理小米推送。

- `createXiaoMiPushNotification(PushNotificationPO po, ConfigAndroid configAndroid)`: 创建并发送小米推送
- `pushXiaoMiRegIDNotification(PushNotificationV2PO po, ConfigAndroid configAndroid)`: 使用V2版本参数发送小米推送
- `xiaoMiPushSender(String appId, String busiId)`: 异步处理小米推送队列

### AdminService

处理管理功能。

- `auth(AuthPO po)`: 验证应用授权并生成认证令牌
- `initConfig()`: 初始化所有应用配置
- `getPushConfig(Long applicationId)`: 获取指定应用的推送配置

## 错误码

| 错误码 | 描述 |
|-------|------|
| 10001 | 应用授权错误 |
| 10002 | 应用信息错误 |
| 10003 | 应用令牌错误 |
| 10004 | 推送令牌长度错误 |
| 10005 | 推送令牌无效错误 |
| 10006 | 推送令牌超时错误 |

## 部署

项目使用Docker进行部署，Dockerfile定义如下：

```dockerfile
FROM openjdk:8-alpine
ARG JAR_FILE
ENV MEM 512m
ENV CONFIG ""
WORKDIR /opt
RUN sed -i "s/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g" /etc/apk/repositories
RUN apk add tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata
ADD target/${JAR_FILE} app.jar
ENTRYPOINT java -Xms${MEM} -Xmx${MEM} -jar app.jar ${CONFIG}
```

也可以使用提供的部署脚本：

```bash
./push-cloud.sh start push-cloud-push-api dev 9970 push-cloud-push-api 512m **************:1180
```

## 技术栈

- Spring Boot: 应用框架
- MyBatis: 数据库访问
- Swagger/OpenAPI: API文档
- Pushy: APNS推送客户端
- 华为推送SDK
- 小米推送SDK (MiPush_SDK_Server_Http2_1.0.14.jar)
- FCM推送

