# push-cloud-push-api 模块文档

## 概述
push-cloud-push-api 模块包含项目中用于推送服务的API实现。

## 主要功能
- **PushApiApplication.java**: 应用启动类。
- **PushCode.java**: 推送代码类。
- **PushConfig.java**: 推送配置类。
- **PushException.java**: 推送异常类。
- **Config.java**: 配置类。
- **HttpConnection.java**: HTTP连接类。
- **ConnectionManager.java**: 连接管理类。
- **AdminController.java**: 管理控制器。
- **PushController.java**: 推送控制器。
- **AdminDao.java**: 管理数据访问对象。
- **PushServiceDao.java**: 推送服务数据访问对象。
- **AuthPO.java**: 授权持久化对象。
- **PushMessagePO.java**: 推送消息持久化对象。
- **PushNotificationPO.java**: 推送通知持久化对象。
- **PushNotificationV2PO.java**: 推送通知V2持久化对象。
- **IosPushScheduler.java**: iOS推送调度器。
- **AdminService.java**: 管理服务。
- **ApnsService.java**: APNs服务。
- **HuaWeiService.java**: 华为服务。
- **PushService.java**: 推送服务。
- **PushUnityService.java**: 推送Unity服务。
- **XiaoMiService.java**: 小米服务。
- **HuaWeiSdkTool.java**: 华为SDK工具。
- **XiaoMiSdkTool.java**: 小米SDK工具。
- **AuthTokenUtil.java**: 认证令牌工具。
- **PushCloudApnsPayloadBuilder.java**: 推送云APNs负载构建器。
- **AuthVO.java**: 授权视图对象。

## 使用方法
请参考源代码中的注释和文档。