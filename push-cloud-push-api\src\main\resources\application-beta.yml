logging:
  file:
    path: /opt/log/${spring.application.name}-${server.port}/
    max-size: 128MB
spring:
  cloud:
    consul:
      enabled: true
      host: *************
      port: 9910
      discovery:
        prefer-ip-address: true
        ip-address: **************
        instance-id: ${spring.cloud.client.hostname}
        service-name: ${spring.application.name}
  datasource:
    master:
      driver-class-name: com.mysql.jdbc.Driver
      url: *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: jvm_wf_cloud_exchange_beta
      password: Mangosteen0!
      maximum-pool-size: 10
    slave:
      driver-class-name: com.mysql.jdbc.Driver
      url: *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: jvm_wf_cloud_exchange_beta
      password: Mangosteen0!
      maximum-pool-size: 10
  redis:
    host: *************
    port: 6379
  kafka:
    bootstrap-servers:
      - **************:9092
      - **************:9092
      - **************:9092
    producer:
      acks: 0
      retries: 0
      properties:
        linger.ms: 1000
common:
  php:
    url:
      refresh-cp-task-url: http://*************:80/Werewolf/task/beta/refreshCPTask.php
      group-lucky-box-send-url: http://developer.53site.com/Werewolf/test/groupLuckyBoxSend.php
      group-versus-group-img-url: http://img.53site.com/Werewolf/group/
      proxy-other-refr-settle-url: http://developer.53site.com/Werewolf/task/beta/proxyOtherRefrSettle.php