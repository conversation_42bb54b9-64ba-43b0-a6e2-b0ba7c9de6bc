package com.mega.push.cloud.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel("认证返回对象")
@Data
public class AuthVO {
    @ApiModelProperty("应用id")
    @NotNull
    private Long appId;
    @ApiModelProperty("应用名称")
    @NotNull
    private String appName;
    @ApiModelProperty("权限密钥")
    @NotNull
    private String authKey;
    @ApiModelProperty("权限密钥")
    @NotNull
    private String authSecret;
    @ApiModelProperty("权限令牌")
    @NotNull
    private String authToken;
}
