#!/bin/bash

usage() {
    echo "Usage: [start|restart|stop]"
    exit 1
}

start() {
    config="--server.port=${3} --spring.profiles.active=${2}"
    docker pull "${6}"/push-cloud/push-cloud-"${1}"/"${4}"
    docker run --name "${1}-${2}-${3}" -h "${1}-${2}-${3}" -p "${3}:${3}" -e MEM="${5}" -e CONFIG="${config}"  -v /opt/log/push-cloud-docker/"${1}":/opt/log -d "${6}"/push-cloud/push-cloud-"${1}"/"${4}"
}

restart() {
   stop "$1" "$2" "$3"
   start "$1" "$2" "$3" "$4" "$5" "$6"
}

stop() {
    docker stop "${1}-${2}-${3}"
    docker container rm "${1}-${2}-${3}"
    docker system prune -af
}

case "$1" in
start)
    start "$2" "$3" "$4" "$5" "$6" "$7"
    ;;
restart)
    restart "$2" "$3" "$4" "$5" "$6" "$7"
    ;;
stop)
    stop "$2" "$3" "$4"
    ;;
*)
    usage
    ;;
esac
