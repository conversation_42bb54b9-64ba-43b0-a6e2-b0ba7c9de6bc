package com.mega.push.cloud.dao;

import com.mega.push.cloud.common.entity.ApplicationAuth;
import com.mega.push.cloud.common.entity.ApplicationInfo;
import com.mega.push.cloud.common.entity.ConfigAndroid;
import com.mega.push.cloud.common.entity.ConfigIos;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 管理服务数据访问对象
 */
@Mapper
public interface AdminDao {
    
    /**
     * 根据应用密钥和密钥获取应用授权信息
     * 
     * @param appKey 应用密钥
     * @param appSecret 应用密钥
     * @return 应用授权信息
     */
    ApplicationAuth getApplicationAuthByKeyAndSecret(@Param("appKey") String appKey, @Param("appSecret") String appSecret);
    
    /**
     * 根据ID获取应用信息
     * 
     * @param id 应用ID
     * @return 应用信息
     */
    ApplicationInfo getApplicationInfoById(@Param("id") Long id);
    
    /**
     * 获取所有应用信息
     * 
     * @return 应用信息列表
     */
    List<ApplicationInfo> getAllApplicationInfo();
    
    /**
     * 根据应用ID获取应用授权列表
     * 
     * @param applicationInfoId 应用ID
     * @return 应用授权列表
     */
    List<ApplicationAuth> getApplicationAuthByAppId(@Param("applicationInfoId") Long applicationInfoId);
    
    /**
     * 根据授权ID获取iOS配置
     * 
     * @param applicationAuthId 授权ID
     * @return iOS配置列表
     */
    List<ConfigIos> getConfigIosByAuthId(@Param("applicationAuthId") Long applicationAuthId);
    
    /**
     * 根据授权ID获取Android配置
     * 
     * @param applicationAuthId 授权ID
     * @return Android配置列表
     */
    List<ConfigAndroid> getConfigAndroidByAuthId(@Param("applicationAuthId") Long applicationAuthId);
}