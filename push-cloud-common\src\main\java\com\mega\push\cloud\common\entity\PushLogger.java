package com.mega.push.cloud.common.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "push_logger")
public class PushLogger {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 推送开始时间
     */
    @Column(name = "createtime")
    private Date createtime;

    /**
     * 推送结束时间
     */
    @Column(name = "endtime")
    private Date endtime;

    /**
     * app ID
     */
    @Column(name = "app_id")
    private String appId;

    /**
     * 业务标识
     */
    @Column(name = "busi_id")
    private String busiId;

    /**
     * 推送内容
     */
    @Column(name = "push_apns")
    private String pushApns;
}