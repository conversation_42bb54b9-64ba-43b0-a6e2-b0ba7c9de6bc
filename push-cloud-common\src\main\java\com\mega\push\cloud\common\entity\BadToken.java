package com.mega.push.cloud.common.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "bad_token")
public class BadToken {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    @Column(name = "topic")
    private String topic;

    @Column(name = "token")
    private String token;

    @Column(name = "createtime")
    private Date createtime;

    @Column(name = "reason")
    private String reason;
}