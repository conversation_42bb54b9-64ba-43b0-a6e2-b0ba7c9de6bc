package com.mega.push.cloud.common.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "config_ios")
public class ConfigIos {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Integer id;

    @Column(name = "application_auth_id")
    private Long applicationAuthId;

    /**
     * 苹果应用唯一主键
     */
    @Column(name = "app_id")
    private String appId;

    /**
     * 推送证书密码
     */
    @Column(name = "cer_pwd")
    private String cerPwd;

    /**
     * 业务表识
     */
    @Column(name = "busi_id")
    private String busiId;

    /**
     * 证书生成时间
     */
    @Column(name = "cer_create_time")
    private Date cerCreateTime;

    /**
     * 证书超时时间
     */
    @Column(name = "cer_expire_time")
    private Date cerExpireTime;

    /**
     * 推送证书路径
     */
    @Column(name = "cer_path")
    private String cerPath;
}