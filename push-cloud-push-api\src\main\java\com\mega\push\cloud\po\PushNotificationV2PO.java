package com.mega.push.cloud.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;



@ApiModel("通用推送入参第二版")
@Data
public class PushNotificationV2PO {
    @ApiModelProperty("设备token数组")
    @NotNull
    private List<String> deviceList;

    @ApiModelProperty("应用id")
    @NotNull
    private Long appId;


    @ApiModelProperty("权限密钥")
    @NotNull
    private String authSecret;

    @ApiModelProperty("权限令牌")
    @NotNull
    private String authToken;
    
    @ApiModelProperty("具体推送参数")
    @NotNull
    private PushMessagePO messagePO;  
}
