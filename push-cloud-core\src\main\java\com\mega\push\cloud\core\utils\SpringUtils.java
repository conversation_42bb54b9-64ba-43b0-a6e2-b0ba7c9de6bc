package com.mega.push.cloud.core.utils;

import java.util.Locale;

import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;

public class SpringUtils {

    private static ApplicationContext applicationContext;

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public static void setApplicationContext(ApplicationContext applicationContext) {
        SpringUtils.applicationContext = applicationContext;
    }

    public static String getLocaleMessage(Integer code) {
        // return applicationContext.getBean(MessageSource.class).getMessage(code + "", null,
        //         HttpUtils.getRequest().getLocale());
        MessageSource messageSource = applicationContext.getBean(MessageSource.class);
        Locale locale;
        try {
            locale = HttpUtils.getRequest().getLocale();
        } catch (Exception e) {
            locale = Locale.CHINA;
        }
        return messageSource.getMessage(code.toString(), null, locale);

    }
}
