package com.mega.push.cloud.common.entity;

import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "application_auth")
public class ApplicationAuth {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    @Column(name = "application_info_id")
    private Long applicationInfoId;

    /**
     * 类型 0:IOS 1:FCM 2:华为 3:小米
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 推送权限密钥
     */
    @Column(name = "appkey")
    private String appkey;

    /**
     * 推送权限密钥
     */
    @Column(name = "appscret")
    private String appscret;

    /**
     * 删除标志
     */
    @Column(name = "delsign")
    private Boolean delsign;
}