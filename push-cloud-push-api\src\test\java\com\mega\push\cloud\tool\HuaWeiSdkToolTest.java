package com.mega.push.cloud.tool;

import com.huawei.push.reponse.SendResponse;
import com.mega.push.cloud.po.PushMessagePO;
import com.xiaomi.xmpush.server.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
@Slf4j
public class HuaWeiSdkToolTest {
    @Test
    public void testSendRegPushWithNewSender() throws Exception {
        // 准备测试数据
        List<String> deviceLis = new ArrayList<>();
        deviceLis.add("IQAAAACy1KyGAAD3LMnej9kJFPcdNvU1lwpDgoVG1B2SSPc7QgCF6uIrouhEj_8k53z-rZZgHr__YLiaQma5WaOb809N0-2IUtFKnVTlIEu7VyHmTA");

        PushMessagePO pushMessagePO = new PushMessagePO();
        pushMessagePO.setTitle("测试");
        pushMessagePO.setDescription("测试内容");
        pushMessagePO.setAndroidActivityType(1);
        // 执行测试方法
        SendResponse result = HuaWeiSdkTool.sendNotification("113283105", "3ea125981517ea10dd6e999bfd47b0d54f569aba021fc68d99182968058bef7f", deviceLis, pushMessagePO);
        log.info("测试结果：", result);
    }
}
