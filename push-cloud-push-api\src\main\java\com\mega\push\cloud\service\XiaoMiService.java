package com.mega.push.cloud.service;


//import com.huawei.push.reponse.SendResponse;
import com.mega.push.cloud.common.entity.ConfigAndroid;
import com.mega.push.cloud.common.entity.PushLogger;
// import com.mega.push.cloud.common.mapper.BadTokenMapper;
import com.mega.push.cloud.common.mapper.PushLoggerMapper;
import com.mega.push.cloud.core.utils.JsonUtils;
// import com.mega.push.cloud.po.PushNotificationPO;
import com.mega.push.cloud.po.PushNotificationV2PO;
import com.mega.push.cloud.tool.XiaoMiSdkTool;
import com.xiaomi.push.sdk.ErrorCode;
import com.xiaomi.xmpush.server.Result;
// import com.xiaomi.xmpush.server.Constants;
// import com.xiaomi.xmpush.server.Message;
import com.xiaomi.xmpush.server.Result;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class XiaoMiService {

    private final PushLoggerMapper pushLoggerMapper;
    // private final BadTokenMapper badTokenMapper;

    @Autowired
    public XiaoMiService(PushLoggerMapper pushLoggerMapper) {
        this.pushLoggerMapper = pushLoggerMapper;
        // this.badTokenMapper = badTokenMapper;
    }

    /**
     * 推送小米 RegID类别通知
     * @param po
     * @param configAndroid
     */
    @SneakyThrows
    public void pushXiaoMiRegIDNotification(PushNotificationV2PO po, ConfigAndroid configAndroid){
        // 1 推送日志
        PushLogger pushLogger = new PushLogger();
        pushLogger.setPushApns(JsonUtils.toJson(po.getMessagePO()));
        pushLogger.setAppId(po.getAppId().toString());
        pushLogger.setCreatetime(new Date());
        pushLogger.setBusiId(configAndroid.getContentType());
        this.pushLoggerMapper.insert(pushLogger);

        // 2 推送内容检测
        // 设置在通知栏展示的通知的标题, 不允许全是空白字符, 长度小于50, 一个中英文字符均计算为1(通知栏消息必填)
        if (po.getMessagePO().getTitle() == null){
            return;
        }
        // 设置在通知栏展示的通知描述, 不允许全是空白字符, 长度小于128, 一个中英文字符均计算为1(通知栏消息必填)
        if (po.getMessagePO().getDescription() == null){
            return;
        }

        // 3发送推送
        Result result = XiaoMiSdkTool.sendRegPush(configAndroid.getClientSecret(), configAndroid.getPackageName(), po.getDeviceList(), po.getMessagePO());
        // 4 推送成功 更新日志
        if (result.getErrorCode().equals(ErrorCode.Success)) {
            PushLogger pushLogger1 = new PushLogger();
            pushLogger1.setId(pushLogger.getId());
            pushLogger1.setEndtime(new Date());
            this.pushLoggerMapper.updateByPrimaryKeySelective(pushLogger1);
        }
    }

}
