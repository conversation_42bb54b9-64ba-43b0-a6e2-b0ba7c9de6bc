<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.mega.push.cloud</groupId>
    <artifactId>push-cloud</artifactId>
    <version>1.0</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.5.RELEASE</version>
    </parent>

    <modules>
        <module>push-cloud-core</module>
        <module>push-cloud-common</module>
        <module>push-cloud-push-api</module>
        <module>push-cloud-generator</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <git.branch>master</git.branch>
        <harbor.url>172.18.219.218:1180</harbor.url>
        <mysql-connector-java.version>5.1.49</mysql-connector-java.version>
        <mapper-spring-boot-starter.version>2.1.5</mapper-spring-boot-starter.version>
        <logbook-spring-boot-starter.version>2.3.0</logbook-spring-boot-starter.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <springfox-boot-starter.version>3.0.0</springfox-boot-starter.version>
        <pushy.version>0.14.2</pushy.version>
        <netty-tcnative-boringssl-static.version>2.0.36.Final</netty-tcnative-boringssl-static.version>
        <dockerfile-maven-plugin.version>1.4.13</dockerfile-maven-plugin.version>
        <mybatis-generator-core.version>1.4.0</mybatis-generator-core.version>
        <mapper.version>4.1.5</mapper.version>
        <freemarker.version>2.3.30</freemarker.version>
        <mybatis-generator-maven-plugin.version>1.3.7</mybatis-generator-maven-plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>Hoxton.SR8</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.mega.push.cloud</groupId>
                <artifactId>push-cloud-core</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.mega.push.cloud</groupId>
                <artifactId>push-cloud-common</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>${mapper-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.zalando</groupId>
                <artifactId>logbook-spring-boot-starter</artifactId>
                <version>${logbook-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox-boot-starter.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.huawei.pushplatform</groupId>-->
<!--                <artifactId>push.java.sample</artifactId>-->
<!--                <version>1.0</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.eatthepath</groupId>
                <artifactId>pushy</artifactId>
                <version>0.14.2</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-tcnative-boringssl-static</artifactId>
                <version>2.0.34.Final</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.2.2</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-core</artifactId>
                <version>${mybatis-generator-core.version}</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper</artifactId>
                <version>${mapper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Repository</name>
            <url>http://gitlab.53site.com:8081/repository/server-releases/</url>
        </repository>
    </distributionManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>${dockerfile-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>default</id>
                            <goals>
                                <goal>build</goal>
                                <goal>push</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <skipDockerInfo>true</skipDockerInfo>
                        <repository>${harbor.url}/${project.parent.artifactId}/${project.artifactId}/${git.branch}
                        </repository>
                        <username>developer</username>
                        <password>LA1954b!</password>
                        <tag>latest</tag>
                        <!-- 设置Docker构建上下文为项目根目录，确保可以访问cerpath目录 -->
                        <contextDirectory>${project.parent.basedir}</contextDirectory>
                        <dockerfile>${project.basedir}/Dockerfile</dockerfile>
                        <buildArgs>
                            <JAR_FILE>${project.artifactId}-${project.version}.jar</JAR_FILE>
                        </buildArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.mybatis.generator</groupId>
                    <artifactId>mybatis-generator-maven-plugin</artifactId>
                    <version>${mybatis-generator-maven-plugin.version}</version>
                    <configuration>
                        <verbose>true</verbose>
                        <overwrite>true</overwrite>
                        <configurationFile>
                            src/main/resources/generatorConfig.xml
                        </configurationFile>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>com.mega.push.cloud</groupId>
                            <artifactId>push-cloud-generator</artifactId>
                            <version>1.0</version>
                        </dependency>
                    </dependencies>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
