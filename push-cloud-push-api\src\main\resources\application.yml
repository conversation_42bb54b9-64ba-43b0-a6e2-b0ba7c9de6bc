logging:
  level:
    org.zalando.logbook: trace
    com.mega.werewolf: info
logbook:
  format:
    style: http
  include:
    - /push/**
    - /admin/**
server:
  port: 9970
  servlet:
    encoding:
      enabled: true
      charset: UTF-8
      force: true
  undertow:
    url-charset: UTF-8
spring:
  profiles:
    active: dev
  application:
    name: push-cloud-push-api-${spring.profiles.active}
  messages:
    encoding: UTF-8
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  jackson:
    default-property-inclusion: non_null
    deserialization:
      fail_on_unknown_properties: false
mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
feign:
  httpclient:
    enabled: false
  okhttp:
    enabled: true
sentry:
  dsn: http://78521e262b554a0ca19720d438647586@**************:9000/18
  environment: ${spring.profiles.active}