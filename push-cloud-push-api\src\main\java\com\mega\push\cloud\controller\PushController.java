package com.mega.push.cloud.controller;

import com.mega.push.cloud.core.Result;
import com.mega.push.cloud.core.Results;
import com.mega.push.cloud.po.PushNotificationPO;
import com.mega.push.cloud.po.PushNotificationV2PO;
import com.mega.push.cloud.service.PushService;
import com.mega.push.cloud.service.PushUnityService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "推送接口")
@RestController
public class PushController {
    private final PushService pushService;

    private final PushUnityService pushUnityService;

    @Autowired
    public PushController(PushService pushService, PushUnityService pushUnityService) {
        this.pushService = pushService;
        this.pushUnityService = pushUnityService;
    }

    @ApiOperation("旧版本推送（只实现ios）")
    @PostMapping("/push/unity/entrance")
    public Result<?> pushUntiyEntrance(@Validated @RequestBody PushNotificationPO po) throws Exception {
        this.pushService.pushUntiyEntrance(po);
        return Results.success();
    }


    @ApiOperation("第二版统一推送（实现ios、android）")
    @PostMapping("/push/api/unity/send")
    public Result<?> pushUntiySend(@Validated @RequestBody PushNotificationV2PO po) throws Exception {
        this.pushUnityService.send(po);
        return Results.success();
    }
}
