package com.mega.push.cloud.common.entity;

import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "application_info")
public class ApplicationInfo {
    /**
     * appID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * app 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 登录名
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 登录密码
     */
    @Column(name = "user_pwd")
    private String userPwd;

    /**
     * 删除标志
     */
    @Column(name = "delsign")
    private Boolean delsign;
}