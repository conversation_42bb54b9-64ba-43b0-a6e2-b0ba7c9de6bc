package com.mega.push.cloud.tool;

import com.mega.push.cloud.core.utils.JsonUtils;
import com.mega.push.cloud.po.PushMessagePO;
import com.xiaomi.push.sdk.ErrorCode;
import com.xiaomi.xmpush.server.Constants;
import com.xiaomi.xmpush.server.Message;
import com.xiaomi.xmpush.server.Result;
import com.xiaomi.xmpush.server.Sender;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.simple.parser.ParseException;

@Slf4j
public class XiaoMiSdkTool {
    private static Map<String, Sender> senderMap = new HashMap<>();

    /**
     * 通过regId推送消息
     * @param APP_SECRET_KEY
     * @param MY_PACKAGE_NAME
     * @param regIdList
     * @param pushMessagePO
     * @return
     * @throws IOException
     * @throws ParseException
     */
    public static Result  sendRegPush(String APP_SECRET_KEY, String MY_PACKAGE_NAME, List<String> regIdList, PushMessagePO pushMessagePO) throws IOException, ParseException{
        Constants.useOfficial();
        Sender sender = null;  
        if(senderMap.containsKey(APP_SECRET_KEY)){  
            sender = senderMap.get(APP_SECRET_KEY);  
        }else{  
            sender = new Sender(APP_SECRET_KEY);  
            senderMap.put(APP_SECRET_KEY, sender);  
        }  

        Map<String, String> extra = new HashMap<>();
        //  渠道id
        if (pushMessagePO.getChannelId() != null){
            extra.put("channel_id", pushMessagePO.getChannelId().toString());
        }else {
            throw new RuntimeException("小米平台必须要入参channelId");
        }

        //  android 点击通知后打开应用
        if (pushMessagePO.getAndroidActivityType() == 1){
            extra.put(Constants.EXTRA_PARAM_NOTIFY_EFFECT,  Constants.NOTIFY_LAUNCHER_ACTIVITY);
        }
        //  android 点击通知后打开的Activity uri
        else if (pushMessagePO.getAndroidActivityType() == 2 ){
            extra.put(Constants.EXTRA_PARAM_NOTIFY_EFFECT,  Constants.NOTIFY_ACTIVITY);
            extra.put(Constants.EXTRA_PARAM_INTENT_URI, pushMessagePO.getAndroidActivityUri());
        }
        //  android 点击通知后打开的网页
        else if (pushMessagePO.getAndroidActivityType() == 3 ){
            extra.put(Constants.EXTRA_PARAM_NOTIFY_EFFECT,  Constants.NOTIFY_WEB);
            extra.put(Constants.EXTRA_PARAM_WEB_URI, pushMessagePO.getAndroidWebUri());
        }
        
        Message message = new Message.Builder()
                    .title(pushMessagePO.getTitle())   // 通知栏标题   长度小于50个字符   一个中英文字符均计算为1个字符
                    .description(pushMessagePO.getDescription())  // 通知栏描述   长度小于128个字符   一个中英文字符均计算为1个字符
                    .payload(pushMessagePO.getPayload())  // 自定义key-value   长度小于1024个字符   一个中英文字符均计算为1个字符
                    .restrictedPackageName(MY_PACKAGE_NAME)
                    .extra(extra) // 通知类别
                    .build();
       Result result =  sender.send(message, regIdList, 3); //发送消息到一组设备上, regids个数不得超过1000个

       if(result.getErrorCode().equals(ErrorCode.Success)){   //发送成功   发送失败会返回错误码  
           log.info("XiaoMi Push Success! : ");  
       }else{  
           log.error("XiaoMi Push Error SendResponse : "+ JsonUtils.toJson(result));   //发送失败会返回错误码
       }  
       return result; 
    }
}
