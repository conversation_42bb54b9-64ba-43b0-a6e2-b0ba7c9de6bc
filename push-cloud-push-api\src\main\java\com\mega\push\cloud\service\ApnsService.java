package com.mega.push.cloud.service;

import com.eatthepath.pushy.apns.ApnsClient;
import com.eatthepath.pushy.apns.PushNotificationResponse;
import com.eatthepath.pushy.apns.util.SimpleApnsPushNotification;
import com.eatthepath.pushy.apns.util.TokenUtil;
import com.eatthepath.pushy.apns.util.concurrent.PushNotificationFuture;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mega.push.cloud.common.entity.ConfigIos;
import com.mega.push.cloud.common.entity.PushLogger;
import com.mega.push.cloud.common.mapper.BadTokenMapper;
import com.mega.push.cloud.common.mapper.PushLoggerMapper;
import com.mega.push.cloud.connection.manager.ConnectionManager;
import com.mega.push.cloud.core.utils.JsonUtils;
import com.mega.push.cloud.po.PushNotificationPO;
import com.mega.push.cloud.po.PushNotificationV2PO;
import com.mega.push.cloud.util.PushCloudApnsPayloadBuilder;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;


@Slf4j
@Service
public class ApnsService {

    private final PushLoggerMapper pushLoggerMapper;
    private final BadTokenMapper badTokenMapper;

    @Autowired
    public ApnsService(PushLoggerMapper pushLoggerMapper, BadTokenMapper badTokenMapper) {
        this.pushLoggerMapper = pushLoggerMapper;
        this.badTokenMapper = badTokenMapper;
    }


    @SneakyThrows
    public void createApnsPushNotification(PushNotificationPO po, ConfigIos configIos) {
        final PushCloudApnsPayloadBuilder payloadBuilder = new PushCloudApnsPayloadBuilder();
        boolean flag = false;
        for (String key : po.getMap().keySet()) {
            if (key.equals("sound")) {
                payloadBuilder.setSound(po.getMap().get(key));
            } else if (key.equals("alert")) {
                payloadBuilder.setAlertBody(po.getMap().get(key));
                flag = true;
            } else {
                payloadBuilder.addApsOtherKey(key, po.getMap().get(key));
            }
        }
//        {"c":"666666.ss","aps":{"badge":1,"alert":{"body":"push-cloud"},"sound":"coins.mp3"},"m":"1","n":"美嘉"}
//        {"aps":{"alert":"12323","badge":1,"sound":"coins.mp3","c":"666666.ss","n":"美嘉","m":"1"}}
        payloadBuilder.setBadgeNumber(1);
        if (!flag) {
            payloadBuilder.setAlertBody("push-cloud");
        }
        payloadBuilder.setPreferStringRepresentationForAlerts(true);
        final String pushApns = payloadBuilder.build();
        PushLogger pushLogger = new PushLogger();
        pushLogger.setPushApns(pushApns);
        pushLogger.setAppId(configIos.getAppId());
        pushLogger.setCreatetime(new Date());
        pushLogger.setBusiId(configIos.getBusiId());
        this.pushLoggerMapper.insert(pushLogger);
        payloadBuilder.addCustomProperty("pushLoggerID", pushLogger.getId());
        ConnectionManager.pushCountMap.put(pushLogger.getId(), po.getDeviceList().size());
        final String payload = payloadBuilder.build();
//        List<BadToken> badTokenList = new ArrayList<>();
        for (String token : po.getDeviceList()) {
            String deviceToken = TokenUtil.sanitizeTokenString(token);
            if (deviceToken.length() > 1) {
                ConnectionManager.pushNotificationMap.get(configIos.getAppId() + "." + configIos.getBusiId()).add(new SimpleApnsPushNotification(deviceToken, configIos.getAppId(), payload));
            } else {
//                BadToken badToken = new BadToken();
//                badToken.setToken(token);
//                badToken.setTopic(configIos.getAppId());
//                badToken.setReason("UnSanitizeTokenString");
//                badTokenList.add(badToken);
                Integer integer = ConnectionManager.pushCountMap.get(pushLogger.getId());
                integer--;
                ConnectionManager.pushCountMap.put(pushLogger.getId(), integer);
            }
        }

//        if (badTokenList.size()>0){
//
//        }

    }


    /**
     * 构造ios通知
     * example: {"c":"666666.ss","aps":{"badge":1,"alert":{"body":"push-cloud"},"sound":"coins.mp3"},"m":"1","n":"美嘉"}
     * {"aps":{"alert":"12323","badge":1,"sound":"coins.mp3","c":"666666.ss","n":"美嘉","m":"1"}}
     *
     * @param po
     * @param configIos
     */
    @SneakyThrows
    public void createApnsPushNotificationV2(PushNotificationV2PO po, ConfigIos configIos) {
        // 1 准备推送通知
        final PushCloudApnsPayloadBuilder payloadBuilder = new PushCloudApnsPayloadBuilder();
        // 设置ios sound
        if (po.getMessagePO().getIosSound() != null) {
            payloadBuilder.setSound(po.getMessagePO().getIosSound());
        }
        // 设置ios alert
        if (po.getMessagePO().getTitle() != null) {
            payloadBuilder.setAlertTitle(po.getMessagePO().getTitle());
        }
        if (po.getMessagePO().getDescription() != null) {
            payloadBuilder.setAlertBody(po.getMessagePO().getDescription());
        } else {
            payloadBuilder.setAlertBody("push-cloud");
        }
        // 设置ios badgeNumber
        if (po.getMessagePO().getBadgeNumber() != null) {
            payloadBuilder.setBadgeNumber(po.getMessagePO().getBadgeNumber());
        }else{
            payloadBuilder.setBadgeNumber(1);
        }
        // 设置ios payload
        payloadBuilder.setPreferStringRepresentationForAlerts(true);
        final String pushApns = payloadBuilder.build();

        // 2 推送日志
        PushLogger pushLogger = new PushLogger();
        pushLogger.setPushApns(pushApns);
        pushLogger.setAppId(configIos.getAppId());
        pushLogger.setCreatetime(new Date());
        pushLogger.setBusiId(configIos.getBusiId());
        this.pushLoggerMapper.insert(pushLogger);
        payloadBuilder.addCustomProperty("pushLoggerID", pushLogger.getId());
        ConnectionManager.pushCountMap.put(pushLogger.getId(), po.getDeviceList().size());
        final String payload = payloadBuilder.build();
//        List<BadToken> badTokenList = new ArrayList<>();

        //3 遍历设备
        for (String token : po.getDeviceList()) {
            String deviceToken = TokenUtil.sanitizeTokenString(token);
            if (deviceToken.length() > 1) {
                SimpleApnsPushNotification pushNotification = new SimpleApnsPushNotification(deviceToken, configIos.getAppId(), payload);
                String appIdBusiId = configIos.getAppId() + "." + configIos.getBusiId();
                // 待发送消息队列
                ConnectionManager.pushNotificationMap.get(appIdBusiId).add(pushNotification);
            } else {
//                BadToken badToken = new BadToken();
//                badToken.setToken(token);
//                badToken.setTopic(configIos.getAppId());
//                badToken.setReason("UnSanitizeTokenString");
//                badTokenList.add(badToken);
                Integer integer = ConnectionManager.pushCountMap.get(pushLogger.getId());
                integer--;
                ConnectionManager.pushCountMap.put(pushLogger.getId(), integer);
            }
        }

//        if (badTokenList.size()>0){
//
//        }

    }


    /**
     * ios定时处理所有app待推送消息
     */
    public void scheduleApnsAppMap() {
        try {
            // 获取配置 使用遍历而不是出队
            Iterator<ConfigIos> iterator = ConnectionManager.apnsAppMap.iterator();
            while (iterator.hasNext()) {
                ConfigIos configIos = iterator.next();
                if (configIos == null) {
                    continue;
                }
                String appIdAuthId = configIos.getAppId() + "." + configIos.getApplicationAuthId();
                String appIdBusiId = configIos.getAppId() + "." + configIos.getBusiId();
                this.sendApnsFromQueue(appIdAuthId, appIdBusiId);
            }
        } catch (Exception e) {
            log.error("定时处理scheduleapnsAppMap", e);
        }
    }

    /**
     * 发送通知的实现
     *
     * @param appIdAuthId
     * @param appIdBusiId
     * @throws InterruptedException
     */
    public void sendApnsFromQueue(String appIdAuthId, String appIdBusiId) throws InterruptedException {

        // 获取待推送消息
        while (true) {
            SimpleApnsPushNotification pushNotification = ConnectionManager.pushNotificationMap.get(appIdBusiId).poll();
            if (pushNotification == null) {
                // log.info("--- schedule ios appIdAuthId: {}, busiId: {} 通知队列空! ---", appIdAuthId, appIdBusiId);
                break;
            }
            // 发送消息
            log.info("--- ios appIdAuthId: {}, send start: {}  ---", appIdAuthId, appIdBusiId,
                    pushNotification.getPayload());
            this.sendByTlsHttp2(pushNotification, appIdAuthId);
            // 发送消息完成
            Thread.sleep(100); // 休眠10ms
        }
    }

    /**
     * tls-http2 发送通知
     *
     * @param pushNotification
     * @param appIdAuthId
     */
    private void sendByTlsHttp2(SimpleApnsPushNotification pushNotification, String appIdAuthId) {
        ApnsClient apnsClient = ConnectionManager.apnsClientMap.get(appIdAuthId);
        final PushNotificationFuture<SimpleApnsPushNotification, PushNotificationResponse<SimpleApnsPushNotification>> sendFuture = apnsClient
                .sendNotification(pushNotification);
        sendFuture.whenComplete((response, cause) -> {
            if (cause != null) {
                log.error("ios appIdAuthId: {} reject error", appIdAuthId, cause);
                handleResponseError(appIdAuthId);
                return;
            }

            this.handleResponseSuccess(response.getPushNotification(), appIdAuthId);
            log.info("--- ios appIdAuthId: {}, send success: {} ---", appIdAuthId, response.toString());
        });
    }

    /**
     * 旧版发送通知
     *
     * @param pushNotification
     * @param appIdAuthId
     */
    private void sendByOld(SimpleApnsPushNotification pushNotification, String appIdAuthId) {
        // 4. 发送通知（与令牌认证方式相同）
        try {
            ApnsClient apnsClient = ConnectionManager.apnsClientMap.get(appIdAuthId);
            final PushNotificationFuture<SimpleApnsPushNotification, PushNotificationResponse<SimpleApnsPushNotification>>
                    sendNotificationFuture = apnsClient.sendNotification(pushNotification);

            final PushNotificationResponse<SimpleApnsPushNotification> pushNotificationResponse =
                    sendNotificationFuture.get();

            if (pushNotificationResponse.isAccepted()) {
               this.handleResponseSuccess(pushNotificationResponse.getPushNotification(), appIdAuthId);
            } else {
               log.error("ios appIdAuthId: {} reject: {}", appIdAuthId, pushNotificationResponse.getRejectionReason());
            }
        } catch (Throwable e) {
           log.error("ios appIdAuthId: {} send error", appIdAuthId, e);
           this.handleResponseError(appIdAuthId);
        }
    }

    /**
     * 处理响应成功
     *
     * @param responsePushNotification
     */
    private void handleResponseSuccess(SimpleApnsPushNotification responsePushNotification, String appIdAuthId) {
        try {
            Map<String, Object> map = JsonUtils.fromJson(responsePushNotification.getPayload(),
                    new TypeReference<Map<String, Object>>() {
                    });
            if (!map.containsKey("pushLoggerID")) {
                return;
            }

            log.info("ios send success! pushLoggerID: {}", map.get("pushLoggerID"));
            Long pushLoggerID = Long.valueOf(String.valueOf(map.get("pushLoggerID")));
            if (ConnectionManager.pushCountMap.containsKey(pushLoggerID)) {
                Integer integer = ConnectionManager.pushCountMap.get(pushLoggerID);
                integer--;
                if (integer < 1) {
                    ConnectionManager.pushCountMap.remove(pushLoggerID);
                    PushLogger pushLogger = new PushLogger();
                    pushLogger.setId(pushLoggerID);
                    pushLogger.setEndtime(new Date());
                    this.pushLoggerMapper.updateByPrimaryKeySelective(pushLogger);
                } else {
                    ConnectionManager.pushCountMap.put(pushLoggerID, integer);
                }
            }
        } catch (Exception e) {
            log.error("Handle response success error for appIdAuthId: {}", appIdAuthId, e);
            this.handleResponseError(appIdAuthId);
        }
    }

    /**
     * 处理响应失败
     *
     * @param appIdAuthId
     * @param error
     */
    private void handleResponseError(String appIdAuthId) {

    }

}
